/**
 * Optimized Calendar Sync Service
 *
 * This service provides enhanced calendar synchronization capabilities:
 * - Removes 3-month limitations
 * - Implements real-time bidirectional sync
 * - Optimized performance with batch operations
 * - Advanced conflict resolution
 * - Intelligent session management
 */

import moment from "moment-timezone";
import { google } from "googleapis";
import { CONFIG } from "../config/environment";
import { throwError } from "../util/functions";
import { GoogleCalendarService } from "./googleCalendar.service";
import { ScheduleService } from "./schedule.service";
import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { TherapistDao } from "../lib/dao/therapist.dao";
import { ClientService } from "./client.service";
import { ScheduleStatus } from "../models/Schedule.model";
import { Types } from "mongoose";

export interface ISyncOptions {
  maxResults?: number;
  timeMin?: string;
  timeMax?: string;
  syncToken?: string;
  incremental?: boolean;
  batchSize?: number;
}

export interface ISyncResult {
  success: boolean;
  syncedEvents: number;
  updatedEvents: number;
  createdEvents: number;
  deletedEvents?: number;
  errors: any[];
  nextSyncToken?: string;
  conflicts?: any[];
}

export interface ISessionGenerationOptions {
  maxMonths?: number; // Remove 3-month limitation
  extendAutomatically?: boolean;
  preservePastSessions?: boolean;
}

export class OptimizedCalendarSyncService {
  private static readonly DEFAULT_BATCH_SIZE = 50;
  private static readonly DEFAULT_MAX_MONTHS = 12; // Increased from 3
  private static readonly SYNC_TOKEN_EXPIRY_HOURS = 24;

  /**
   * Enhanced sync with incremental updates and performance optimization
   */
  static async performIncrementalSync(
    therapistId: string,
    options: ISyncOptions = {}
  ): Promise<ISyncResult> {
    const result: ISyncResult = {
      success: false,
      syncedEvents: 0,
      updatedEvents: 0,
      createdEvents: 0,
      deletedEvents: 0,
      errors: [],
      conflicts: []
    };

    try {
      const googleCalendarData = await GoogleCalendarService.findByTherapist(therapistId);
      if (!googleCalendarData) {
        throw new Error("No Google Calendar data found");
      }

      const oauth2Client = await this.getAuthenticatedClient(googleCalendarData);
      const calendar = google.calendar({ version: "v3", auth: oauth2Client });

      // Use sync token for incremental sync if available
      const syncParams: any = {
        calendarId: "primary",
        maxResults: options.maxResults || 250,
        singleEvents: true,
        orderBy: "startTime"
      };

      if (options.syncToken) {
        syncParams.syncToken = options.syncToken;
      } else {
        syncParams.timeMin = options.timeMin || moment().subtract(1, "month").toISOString();
        syncParams.timeMax = options.timeMax || moment().add(this.DEFAULT_MAX_MONTHS, "months").toISOString();
      }

      const eventsResponse = await calendar.events.list(syncParams);
      const events = eventsResponse.data.items || [];

      // Store next sync token for incremental updates
      result.nextSyncToken = eventsResponse.data.nextSyncToken || undefined;

      // Process events in batches for better performance
      const batches = this.createBatches(events, options.batchSize || this.DEFAULT_BATCH_SIZE);

      for (const batch of batches) {
        const batchResult = await this.processBatch(therapistId, batch);
        result.syncedEvents += batchResult.processed;
        result.updatedEvents += batchResult.updated;
        result.createdEvents += batchResult.created;
        result.deletedEvents! += batchResult.deleted;
        result.errors.push(...batchResult.errors);
        result.conflicts?.push(...batchResult.conflicts);
      }

      result.success = result.errors.length === 0;
      return result;

    } catch (error: any) {
      result.errors.push({
        type: "sync_error",
        message: error.message,
        timestamp: new Date()
      });
      return result;
    }
  }

  /**
   * Generate sessions without 3-month limitation
   */
  static async generateExtendedSessions(
    scheduleData: any,
    options: ISessionGenerationOptions = {}
  ): Promise<any[]> {
    const maxMonths = options.maxMonths || this.DEFAULT_MAX_MONTHS;
    const extendAutomatically = options.extendAutomatically ?? true;

    const sessions: any[] = [];
    const startDate = moment(scheduleData.fromDate);
    const endDate = moment(scheduleData.fromDate).add(maxMonths, "months");

    let currentDate = startDate.clone();

    // Generate sessions based on recurrence pattern
    while (currentDate.isBefore(endDate)) {
      const sessionStart = currentDate.clone();
      const sessionEnd = moment(scheduleData.toDate).clone()
        .year(sessionStart.year())
        .month(sessionStart.month())
        .date(sessionStart.date());

      sessions.push({
        fromDate: sessionStart.toDate(),
        toDate: sessionEnd.toDate(),
        status: ScheduleStatus.PENDING,
        amount: scheduleData.amount || 0,
        _id: new Types.ObjectId()
      });

      // Move to next occurrence based on recurrence pattern
      currentDate = this.getNextOccurrence(currentDate, scheduleData.recurrence);
    }

    return sessions;
  }

  /**
   * Handle rescheduled events with automatic updates
   */
  static async handleRescheduledEvent(
    therapistId: string,
    originalEvent: any,
    updatedEvent: any
  ): Promise<boolean> {
    try {
      // Find the corresponding schedule and recurrence date
      const calendarEvent = await CalendarEventDao.findByGoogleEventId(originalEvent.id);
      if (!calendarEvent) {
        console.log(`No calendar event found for Google event ID: ${originalEvent.id}`);
        return false;
      }

      const schedule = await ScheduleDao.getScheduleById(calendarEvent.scheduleId);
      if (!schedule) {
        console.log(`No schedule found for calendar event: ${calendarEvent._id}`);
        return false;
      }

      // Find the specific recurrence date
      const recurrenceDate = schedule.recurrenceDates.find(
        (rd: any) => rd.calenderEventId?.toString() === calendarEvent._id.toString()
      );

      if (!recurrenceDate) {
        console.log(`No recurrence date found for calendar event: ${calendarEvent._id}`);
        return false;
      }

      // Update the recurrence date with new times
      recurrenceDate.fromDate = new Date(updatedEvent.start.dateTime);
      recurrenceDate.toDate = new Date(updatedEvent.end.dateTime);
      recurrenceDate.status = ScheduleStatus.RESCHEDULED;

      // Update the calendar event
      calendarEvent.start = updatedEvent.start;
      calendarEvent.end = updatedEvent.end;
      calendarEvent.updated = new Date(updatedEvent.updated);

      // Save changes
      await schedule.save();
      await calendarEvent.save();

      console.log(`Successfully updated rescheduled event: ${originalEvent.id}`);
      return true;

    } catch (error: any) {
      console.error(`Error handling rescheduled event: ${error.message}`);
      return false;
    }
  }

  /**
   * Create batches for processing events
   */
  private static createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Process a batch of events with enhanced change detection
   */
  private static async processBatch(
    therapistId: string,
    events: any[]
  ): Promise<{
    processed: number;
    updated: number;
    created: number;
    deleted: number;
    errors: Array<{ eventId: string; error: string }>;
    conflicts: any[];
  }> {
    const result = {
      processed: 0,
      updated: 0,
      created: 0,
      deleted: 0,
      errors: [] as Array<{ eventId: string; error: string }>,
      conflicts: [] as any[]
    };

    for (const event of events) {
      try {
        // Check if event is cancelled/deleted
        if (event.status === 'cancelled') {
          await this.handleDeletedEvent(therapistId, event);
          result.deleted++;
          result.processed++;
          continue;
        }

        const existingEvent = await CalendarEventDao.findByGoogleEventId(event.id);

        if (existingEvent) {
          // Check if event was modified
          const eventUpdated = new Date(event.updated);
          const existingUpdated = new Date(existingEvent.updated);

          if (eventUpdated > existingUpdated) {
            await this.handleRescheduledEvent(therapistId, existingEvent, event);
            result.updated++;
          }
        } else {
          // Check if this is a detached series event (rescheduled)
          const isDetachedEvent = this.isDetachedSeriesEvent(event);
          if (isDetachedEvent) {
            const handled = await this.handleDetachedSeriesEvent(therapistId, event);
            if (handled) {
              result.updated++;
            } else {
              // If not handled as detached, check if it's a duplicate
              const isDuplicate = await this.checkForDuplicateSession(therapistId, event);
              if (!isDuplicate) {
                // Create new session only if not duplicate
                if (event.attendees && event.attendees.length > 0) {
                  try {
                    const sessionCreated = await this.createSessionFromGoogleEvent(therapistId, event);
                    if (sessionCreated) {
                      result.created++;
                    }
                  } catch (error: any) {
                    result.errors.push({
                      eventId: event.id,
                      error: `Failed to create session: ${error?.message || 'Unknown error'}`
                    });
                  }
                }
              } else {
                console.log(`Skipping duplicate session for event: ${event.id}`);
              }
            }
          } else {
            // Check for duplicates before creating new sessions
            const isDuplicate = await this.checkForDuplicateSession(therapistId, event);
            if (!isDuplicate) {
              // New event - create if it has attendees
              if (event.attendees && event.attendees.length > 0) {
                try {
                  // Create session from Google Calendar event
                  const sessionCreated = await this.createSessionFromGoogleEvent(therapistId, event);
                  if (sessionCreated) {
                    result.created++;
                  }
                } catch (error: any) {
                  result.errors.push({
                    eventId: event.id,
                    error: `Failed to create session: ${error?.message || 'Unknown error'}`
                  });
                }
              }
            } else {
              console.log(`Skipping duplicate session for event: ${event.id}`);
            }
          }
        }

        result.processed++;
      } catch (error: any) {
        result.errors.push({
          eventId: event.id,
          error: error.message
        });
      }
    }

    return result;
  }

  /**
   * Get authenticated Google Calendar client
   */
  private static async getAuthenticatedClient(googleCalendarData: any) {
    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    oauth2Client.setCredentials({
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token
    });

    return oauth2Client;
  }

  /**
   * Create session from Google Calendar event
   */
  private static async createSessionFromGoogleEvent(therapistId: string, event: any): Promise<boolean> {
    try {
      const ScheduleDao = require("../lib/dao/schedule.dao").ScheduleDao;
      const CalendarEventDao = require("../lib/dao/calendarEvent.dao").CalendarEventDao;
      const { ScheduleStatus } = require("../models/Schedule.model");

      // Extract attendee email (first attendee that's not the organizer)
      const attendeeEmail = event.attendees?.find((attendee: any) =>
        attendee.email !== event.organizer?.email
      )?.email;

      if (!attendeeEmail) {
        console.log(`No valid attendee email found for event ${event.id}`);
        return false;
      }

      // Create schedule data from Google Calendar event
      const scheduleData = {
        therapistId: therapistId,
        email: attendeeEmail,
        name: event.summary || "Google Calendar Session",
        description: event.description || "",
        fromPublicCalender: true,
        syncWithCalender: true,
        location: event.location || "online",
        recurrenceDates: [{
          fromDate: new Date(event.start.dateTime || event.start.date),
          toDate: new Date(event.end.dateTime || event.end.date),
          status: ScheduleStatus.CONFIRMED,
          syncStatus: true
        }],
        scheduleId: `gc_${event.id}_${Date.now()}` // Unique schedule ID
      };

      // Create the schedule
      const schedule = await ScheduleDao.createSchedule(scheduleData);
      if (!schedule) {
        console.log(`Failed to create schedule for event ${event.id}`);
        return false;
      }

      // Create calendar event record
      const calendarEventData = {
        therapistId: therapistId,
        scheduleId: schedule._id,
        scheduleRecId: schedule.recurrenceDates[0]._id,
        etag: event.etag,
        id: event.id,
        status: event.status,
        htmlLink: event.htmlLink,
        created: new Date(event.created),
        updated: new Date(event.updated),
        summary: event.summary,
        description: event.description,
        location: event.location,
        creator: event.creator,
        organizer: event.organizer,
        start: event.start,
        end: event.end,
        iCalUID: event.iCalUID,
        sequence: event.sequence,
        attendees: event.attendees,
        reminders: event.reminders,
        eventType: event.eventType,
        hangoutLink: event.hangoutLink,
        conferenceData: event.conferenceData,
        visibility: event.visibility
      };

      const calendarEvent = await CalendarEventDao.createCalendarEvent(calendarEventData);
      if (calendarEvent) {
        // Link the calendar event to the schedule
        schedule.recurrenceDates[0].calenderEventId = calendarEvent._id;
        await schedule.save();

        console.log(`Successfully created session from Google Calendar event ${event.id}`);
        return true;
      }

      return false;
    } catch (error: any) {
      console.error(`Error creating session from Google event ${event.id}:`, error.message);
      return false;
    }
  }

  /**
   * Handle deleted/cancelled events from Google Calendar
   */
  private static async handleDeletedEvent(therapistId: string, event: any): Promise<void> {
    try {
      console.log(`Handling deleted event: ${event.id} for therapist ${therapistId}`);

      // Find the calendar event in database
      const calendarEvent = await CalendarEventDao.findByGoogleEventId(event.id);
      if (!calendarEvent) {
        console.log(`No calendar event found for deleted Google event: ${event.id}`);
        return;
      }

      // Find the associated schedule
      const schedule = await ScheduleDao.getScheduleById(calendarEvent.scheduleId);
      if (!schedule) {
        console.log(`No schedule found for deleted calendar event: ${event.id}`);
        return;
      }

      // Find the specific recurrence date
      const recurrenceDate = schedule.recurrenceDates.find(
        (rd: any) => rd.calenderEventId && rd.calenderEventId.toString() === calendarEvent._id.toString()
      );

      if (recurrenceDate) {
        // Mark the specific session as cancelled
        recurrenceDate.status = ScheduleStatus.CANCELLED;
        // Add cancellation info to the other field since cancelledAt doesn't exist in interface
        (recurrenceDate as any).other = {
          ...((recurrenceDate as any).other || {}),
          isRefunded: (recurrenceDate as any).other?.isRefunded || false,
          isRescheduled: (recurrenceDate as any).other?.isRescheduled || false,
          cancelledAt: new Date(),
          cancelReason: 'Deleted from Google Calendar'
        };

        await schedule.save();
        console.log(`Marked session as cancelled for deleted event: ${event.id}`);
      }

      // Update the calendar event status
      calendarEvent.status = 'cancelled';
      // Add deletedAt to the calendar event using type assertion
      (calendarEvent as any).deletedAt = new Date();
      await calendarEvent.save();

    } catch (error: any) {
      console.error(`Error handling deleted event ${event.id}:`, error.message);
    }
  }

  /**
   * Check if event is a detached series event (rescheduled from Google Calendar)
   */
  private static isDetachedSeriesEvent(event: any): boolean {
    // Check for detached ID pattern (contains underscore with timestamp)
    const detachedIdPattern = /^[a-zA-Z0-9]+_\d{8}T\d{6}Z$/;
    return detachedIdPattern.test(event.id);
  }

  /**
   * Handle detached series events (events rescheduled from Google Calendar)
   */
  private static async handleDetachedSeriesEvent(therapistId: string, event: any): Promise<boolean> {
    try {
      console.log(`🔄 Handling detached series event: ${event.id} for therapist ${therapistId}`);

      // First, try to update existing schedule by finding the original series
      const updated = await this.updateOriginalScheduleFromDetachedEvent(therapistId, event);
      if (updated) {
        console.log(`✅ Successfully updated original schedule for detached event: ${event.id}`);
        return true;
      }

      // Fallback: Extract base ID from detached ID
      const baseId = event.id.split('_')[0];

      // Find existing events that might match this rescheduled event
      const existingEvents = await CalendarEventDao.getTherapistCalendarEvents(therapistId);

      // Look for matching events based on attendees and summary
      const matchingEvent = existingEvents.find((existing: any) => {
        return this.isLikelyRescheduledEvent(event, existing);
      });

      if (matchingEvent) {
        console.log(`✅ Found matching event for detached series: ${matchingEvent.id} -> ${event.id}`);

        // Update the existing session with new timing
        const updated = await this.updateExistingSessionFromDetached(therapistId, event, matchingEvent);

        if (updated) {
          console.log(`✅ Successfully updated existing session for detached event: ${event.id}`);
          return true;
        }
      } else {
        console.log(`❌ No matching event found for detached series: ${event.id}`);
      }

      return false;
    } catch (error: any) {
      console.error(`💥 Error handling detached series event ${event.id}:`, error.message);
      return false;
    }
  }

  /**
   * Update original schedule from detached event (for entire series rescheduling)
   */
  private static async updateOriginalScheduleFromDetachedEvent(therapistId: string, detachedEvent: any): Promise<boolean> {
    try {
      console.log(`🔍 Looking for original schedule to update from detached event: ${detachedEvent.id}`);

      // Get attendee email (client email)
      const attendeeEmail = detachedEvent.attendees?.find((a: any) => a.email !== detachedEvent.organizer?.email)?.email;
      if (!attendeeEmail) {
        console.log(`❌ No attendee email found in detached event`);
        return false;
      }

      console.log(`   Searching for schedule with attendee email: ${attendeeEmail}`);

      // Find all schedules for this therapist with matching email
      const allSchedules = await ScheduleDao.getScheduleByTherapistId(therapistId);
      const matchingSchedules = allSchedules.filter((s: any) =>
        s.email === attendeeEmail || (s.additionalEmails && s.additionalEmails.includes(attendeeEmail))
      );

      if (matchingSchedules.length === 0) {
        console.log(`❌ No schedules found with email: ${attendeeEmail}`);
        return false;
      }

      console.log(`✅ Found ${matchingSchedules.length} schedule(s) with matching email`);

      // Extract the date from the detached event ID to find the right recurrence date
      const detachedEventDate = this.extractDateFromDetachedEventId(detachedEvent.id);
      if (!detachedEventDate) {
        console.log(`❌ Could not extract date from detached event ID: ${detachedEvent.id}`);
        return false;
      }

      console.log(`   Detached event date: ${detachedEventDate.toISOString()}`);

      // Find the schedule that has a recurrence date matching this detached event date
      for (const schedule of matchingSchedules) {
        for (let i = 0; i < schedule.recurrenceDates.length; i++) {
          const recurrenceDate = schedule.recurrenceDates[i];
          const recurrenceDateTime = new Date(recurrenceDate.fromDate);

          // Check if this recurrence date matches the detached event date (within same day)
          const isSameDay = recurrenceDateTime.toDateString() === detachedEventDate.toDateString();

          if (isSameDay) {
            console.log(`✅ Found matching recurrence date in schedule ${schedule._id}, updating...`);

            // Update this specific recurrence date with new timing
            schedule.recurrenceDates[i].fromDate = new Date(detachedEvent.start.dateTime);
            schedule.recurrenceDates[i].toDate = new Date(detachedEvent.end.dateTime);
            schedule.recurrenceDates[i].status = ScheduleStatus.RESCHEDULED;

            // Mark as rescheduled
            (schedule.recurrenceDates[i].other as any) = {
              ...(schedule.recurrenceDates[i].other || {}),
              isRefunded: schedule.recurrenceDates[i].other?.isRefunded || false,
              isRescheduled: true,
              rescheduledAt: new Date(),
              rescheduledFrom: 'Google Calendar'
            };

            await schedule.save();

            console.log(`✅ Successfully updated recurrence date for ${detachedEventDate.toDateString()}`);
            return true;
          }
        }
      }

      console.log(`❌ No matching recurrence date found for ${detachedEventDate.toDateString()}`);
      return false;

    } catch (error: any) {
      console.error(`💥 Error updating original schedule from detached event:`, error.message);
      return false;
    }
  }

  /**
   * Extract date from detached event ID
   */
  private static extractDateFromDetachedEventId(detachedEventId: string): Date | null {
    try {
      // Detached event IDs look like: 56664v0vj3nr44kgchajf61041_20250623T034500Z
      const parts = detachedEventId.split('_');
      if (parts.length < 2) {
        return null;
      }

      const dateTimePart = parts[1]; // 20250623T034500Z

      // Parse the date part: 20250623T034500Z
      const year = parseInt(dateTimePart.substring(0, 4));
      const month = parseInt(dateTimePart.substring(4, 6)) - 1; // Month is 0-indexed
      const day = parseInt(dateTimePart.substring(6, 8));
      const hour = parseInt(dateTimePart.substring(9, 11));
      const minute = parseInt(dateTimePart.substring(11, 13));
      const second = parseInt(dateTimePart.substring(13, 15));

      return new Date(year, month, day, hour, minute, second);
    } catch (error: any) {
      console.error(`Error extracting date from detached event ID ${detachedEventId}:`, error.message);
      return null;
    }
  }

  /**
   * Check if events are likely the same (for rescheduled detection)
   */
  private static isLikelyRescheduledEvent(googleEvent: any, existingEvent: any): boolean {
    // Compare attendees
    const googleAttendees = (googleEvent.attendees || []).map((a: any) => a.email).sort();
    const existingAttendees = (existingEvent.attendees || []).map((a: any) => a.email).sort();

    if (JSON.stringify(googleAttendees) !== JSON.stringify(existingAttendees)) {
      return false;
    }

    // Compare summary similarity
    if (googleEvent.summary && existingEvent.summary) {
      const summaryMatch = googleEvent.summary.toLowerCase().includes(existingEvent.summary.toLowerCase()) ||
                          existingEvent.summary.toLowerCase().includes(googleEvent.summary.toLowerCase());
      return summaryMatch;
    }

    return true;
  }

  /**
   * Check for duplicate sessions to prevent creating duplicates during manual sync
   */
  private static async checkForDuplicateSession(therapistId: string, event: any): Promise<boolean> {
    try {
      // Get all existing schedules for this therapist
      const existingSchedules = await ScheduleDao.getScheduleByTherapistId(therapistId);

      const eventStart = new Date(event.start.dateTime);
      const eventEnd = new Date(event.end.dateTime);
      const eventAttendees = (event.attendees || []).map((a: any) => a.email).sort();

      for (const schedule of existingSchedules) {
        for (const recurrenceDate of schedule.recurrenceDates) {
          // Check if timing and attendees match
          const scheduleStart = new Date(recurrenceDate.fromDate);
          const scheduleEnd = new Date(recurrenceDate.toDate);

          // Check if times match (within 1 minute tolerance)
          const startTimeDiff = Math.abs(eventStart.getTime() - scheduleStart.getTime());
          const endTimeDiff = Math.abs(eventEnd.getTime() - scheduleEnd.getTime());

          if (startTimeDiff < 60000 && endTimeDiff < 60000) { // 1 minute tolerance
            // Check if attendees match
            const scheduleAttendees = [schedule.email, ...(schedule.additionalEmails || [])].sort();

            if (JSON.stringify(eventAttendees) === JSON.stringify(scheduleAttendees)) {
              console.log(`Duplicate session detected for event ${event.id} - matches existing session`);
              return true;
            }
          }
        }
      }

      return false;
    } catch (error: any) {
      console.error(`Error checking for duplicate session ${event.id}:`, error.message);
      return false;
    }
  }

  /**
   * Update existing session from detached series event
   */
  private static async updateExistingSessionFromDetached(
    therapistId: string,
    detachedEvent: any,
    originalEvent: any
  ): Promise<boolean> {
    try {
      console.log(`🔄 Attempting to update existing session for detached event: ${detachedEvent.id}`);
      console.log(`   Original event ID: ${originalEvent.id}`);
      console.log(`   Original event scheduleId: ${originalEvent.scheduleId}`);

      // Find the schedule associated with the original event
      const schedule = await ScheduleDao.getScheduleById(originalEvent.scheduleId);
      if (!schedule) {
        console.log(`❌ No schedule found for original event: ${originalEvent.id}`);

        // Try alternative lookup methods
        console.log(`🔍 Trying alternative schedule lookup methods...`);

        // Method 1: Find by therapist and attendee email
        const attendeeEmail = detachedEvent.attendees?.find((a: any) => a.email !== detachedEvent.organizer?.email)?.email;
        if (attendeeEmail) {
          console.log(`   Looking for schedule with attendee email: ${attendeeEmail}`);
          const schedulesByEmail = await ScheduleDao.getScheduleByTherapistId(therapistId);
          const matchingSchedule = schedulesByEmail.find((s: any) =>
            s.email === attendeeEmail || (s.additionalEmails && s.additionalEmails.includes(attendeeEmail))
          );

          if (matchingSchedule) {
            console.log(`✅ Found schedule by email: ${matchingSchedule._id}`);
            return await this.updateScheduleWithDetachedEvent(matchingSchedule, detachedEvent, originalEvent);
          }
        }

        // Method 2: Find by timing pattern (original session time)
        const originalStartTime = new Date(originalEvent.start?.dateTime || originalEvent.start?.date);
        console.log(`   Looking for schedule with original start time: ${originalStartTime}`);

        const allSchedules = await ScheduleDao.getScheduleByTherapistId(therapistId);
        for (const sched of allSchedules) {
          for (const recDate of sched.recurrenceDates) {
            const recStartTime = new Date(recDate.fromDate);
            const timeDiff = Math.abs(recStartTime.getTime() - originalStartTime.getTime());

            // If times match within 1 hour (accounting for timezone differences)
            if (timeDiff < 3600000) {
              console.log(`✅ Found schedule by timing pattern: ${sched._id}`);
              return await this.updateScheduleWithDetachedEvent(sched, detachedEvent, originalEvent, recDate);
            }
          }
        }

        console.log(`❌ Could not find matching schedule using any method`);
        return false;
      }

      return await this.updateScheduleWithDetachedEvent(schedule, detachedEvent, originalEvent);
    } catch (error: any) {
      console.error(`💥 Error updating existing session from detached event ${detachedEvent.id}:`, error.message);
      return false;
    }
  }

  /**
   * Update schedule with detached event data
   */
  private static async updateScheduleWithDetachedEvent(
    schedule: any,
    detachedEvent: any,
    originalEvent: any,
    specificRecurrenceDate?: any
  ): Promise<boolean> {
    try {
      // Find the specific recurrence date
      let recurrenceDate = specificRecurrenceDate;

      if (!recurrenceDate) {
        recurrenceDate = schedule.recurrenceDates.find(
          (rd: any) => rd.calenderEventId && rd.calenderEventId.toString() === originalEvent._id.toString()
        );
      }

      if (recurrenceDate) {
        console.log(`✅ Found matching recurrence date, updating session timing`);

        // Update the session timing
        recurrenceDate.fromDate = new Date(detachedEvent.start.dateTime);
        recurrenceDate.toDate = new Date(detachedEvent.end.dateTime);
        recurrenceDate.status = ScheduleStatus.RESCHEDULED;

        // Mark as rescheduled in the other field
        (recurrenceDate as any).other = {
          ...((recurrenceDate as any).other || {}),
          isRefunded: (recurrenceDate as any).other?.isRefunded || false,
          isRescheduled: true,
          rescheduledAt: new Date(),
          rescheduledFrom: 'Google Calendar'
        };

        await schedule.save();

        // Update the calendar event
        originalEvent.start = detachedEvent.start;
        originalEvent.end = detachedEvent.end;
        originalEvent.updated = new Date(detachedEvent.updated);
        originalEvent.id = detachedEvent.id; // Update to new detached ID
        (originalEvent as any).isDetachedSeries = true;
        (originalEvent as any).originalEventId = originalEvent.id;

        await originalEvent.save();

        console.log(`✅ Successfully updated existing session for detached event: ${detachedEvent.id}`);
        return true;
      } else {
        console.log(`❌ No matching recurrence date found in schedule ${schedule._id}`);
        return false;
      }
    } catch (error: any) {
      console.error(`💥 Error in updateScheduleWithDetachedEvent:`, error.message);
      return false;
    }
  }

  /**
   * Calculate next occurrence based on recurrence pattern
   */
  private static getNextOccurrence(currentDate: moment.Moment, recurrence: string): moment.Moment {
    const nextDate = currentDate.clone();

    switch (recurrence) {
      case "Every Day":
        return nextDate.add(1, "day");
      case "Every Week":
        return nextDate.add(1, "week");
      case "Every Two Weeks":
        return nextDate.add(2, "weeks");
      case "Every Month":
        return nextDate.add(1, "month");
      default:
        return nextDate.add(1, "week"); // Default to weekly
    }
  }
}
