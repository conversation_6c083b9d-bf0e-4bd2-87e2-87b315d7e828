/**
 * Test Script for Enhanced Webhook Sync System
 * 
 * This script tests the webhook-based synchronization functionality
 * Run with: npm run test:webhook
 */

import { CalendarWebhookService } from '../services/calendarWebhook.service';
import { EnhancedRescheduleDetectorService } from '../services/enhancedRescheduleDetector.service';
import { OptimizedCalendarSyncService } from '../services/optimizedCalendarSync.service';
import { WebhookTestService } from '../tests/webhookTest';

class WebhookSyncTester {
  
  /**
   * Test webhook setup and basic functionality
   */
  static async testWebhookSetup(therapistId: string): Promise<void> {
    console.log('🔧 Testing Webhook Setup...');
    
    try {
      // Test webhook setup
      const webhook = await CalendarWebhookService.setupWebhook(therapistId);
      
      if (webhook) {
        console.log('✅ Webhook setup successful:', {
          channelId: webhook.id,
          expiration: new Date(parseInt(webhook.expiration)).toISOString(),
          resourceUri: webhook.resourceUri
        });
        
        // Test webhook status
        const activeWebhooks = await CalendarWebhookService.getActiveWebhooks();
        console.log(`✅ Active webhooks: ${activeWebhooks.length}`);
        
        return webhook;
      } else {
        console.log('❌ Webhook setup failed');
      }
    } catch (error: any) {
      console.error('❌ Error testing webhook setup:', error.message);
    }
  }

  /**
   * Test comprehensive sync functionality
   */
  static async testComprehensiveSync(therapistId: string): Promise<void> {
    console.log('🔄 Testing Comprehensive Sync...');
    
    try {
      const syncResult = await OptimizedCalendarSyncService.performIncrementalSync(
        therapistId,
        { 
          incremental: true, 
          maxResults: 100 
        }
      );

      console.log('✅ Sync completed:', {
        success: syncResult.success,
        syncedEvents: syncResult.syncedEvents,
        updatedEvents: syncResult.updatedEvents,
        createdEvents: syncResult.createdEvents,
        deletedEvents: syncResult.deletedEvents || 0,
        errors: syncResult.errors.length,
        conflicts: syncResult.conflicts?.length || 0
      });

      if (syncResult.errors.length > 0) {
        console.log('⚠️ Sync errors:', syncResult.errors);
      }

    } catch (error: any) {
      console.error('❌ Error testing comprehensive sync:', error.message);
    }
  }

  /**
   * Test detached series detection
   */
  static async testDetachedSeriesDetection(): Promise<void> {
    console.log('🔍 Testing Detached Series Detection...');
    
    // Mock detached series events
    const mockEvents = [
      {
        id: 'e3dmdlbilb9rjf3hb9qaicf75_20250708T064500Z',
        summary: 'Therapy Session - John Doe',
        start: { dateTime: '2025-07-08T06:45:00Z' },
        end: { dateTime: '2025-07-08T07:45:00Z' },
        attendees: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ],
        iCalUID: '<EMAIL>',
        updated: new Date().toISOString(),
        status: 'confirmed'
      },
      {
        id: 'abc123def456_20250709T064500Z',
        summary: 'Therapy Session - Jane Smith',
        start: { dateTime: '2025-07-09T06:45:00Z' },
        end: { dateTime: '2025-07-09T07:45:00Z' },
        attendees: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ],
        iCalUID: '<EMAIL>',
        updated: new Date().toISOString(),
        status: 'confirmed',
        recurrence: ['RRULE:FREQ=WEEKLY;COUNT=10']
      }
    ];

    try {
      const detectionResult = await EnhancedRescheduleDetectorService.detectAdvancedReschedules(
        'test-therapist-id',
        mockEvents
      );

      console.log('✅ Detection Results:', {
        newEvents: detectionResult.newEvents.length,
        rescheduledEvents: detectionResult.rescheduledEvents.length,
        deletedEvents: detectionResult.deletedEvents.length,
        seriesChanges: detectionResult.seriesChanges.length,
        conflictingEvents: detectionResult.conflictingEvents.length
      });

      // Log series changes details
      detectionResult.seriesChanges.forEach((change, index) => {
        console.log(`📋 Series Change ${index + 1}:`, {
          seriesId: change.seriesId,
          changeType: change.changeType,
          isDetachedSeries: change.isDetachedSeries,
          originalEventId: change.originalEventId,
          affectedInstances: change.affectedInstances.length
        });
      });

    } catch (error: any) {
      console.error('❌ Error testing detached series detection:', error.message);
    }
  }

  /**
   * Test webhook notification processing
   */
  static async testWebhookNotification(therapistId: string): Promise<void> {
    console.log('📨 Testing Webhook Notification Processing...');
    
    // Mock webhook headers
    const mockHeaders = {
      'x-goog-channel-id': `therapist_${therapistId}_${Date.now()}`,
      'x-goog-resource-state': 'exists',
      'x-goog-resource-id': 'test-resource-id',
      'x-goog-resource-uri': 'https://www.googleapis.com/calendar/v3/calendars/primary/events',
      'x-goog-channel-token': therapistId,
      'x-goog-event-type': 'update',
      'x-goog-event-id': 'test-event-id'
    };

    try {
      const result = await CalendarWebhookService.processWebhookNotification(mockHeaders);
      
      if (result.success) {
        console.log('✅ Webhook notification processed successfully');
      } else {
        console.log('⚠️ Webhook notification processing failed:', result.message);
      }

    } catch (error: any) {
      console.error('❌ Error testing webhook notification:', error.message);
    }
  }

  /**
   * Test deleted event handling
   */
  static async testDeletedEventHandling(): Promise<void> {
    console.log('🗑️ Testing Deleted Event Handling...');
    
    // Mock deleted events
    const mockDeletedEvents = [
      {
        id: 'deleted-event-1',
        summary: 'Cancelled Session',
        status: 'cancelled',
        start: { dateTime: '2025-07-10T10:00:00Z' },
        end: { dateTime: '2025-07-10T11:00:00Z' },
        attendees: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      }
    ];

    try {
      console.log(`📝 Processing ${mockDeletedEvents.length} deleted events...`);
      
      for (const deletedEvent of mockDeletedEvents) {
        console.log(`- Processing: ${deletedEvent.id} (${deletedEvent.summary})`);
        // In real implementation, this would call the actual deletion handling
      }
      
      console.log('✅ Deleted events processed successfully');

    } catch (error: any) {
      console.error('❌ Error testing deleted event handling:', error.message);
    }
  }

  /**
   * Run comprehensive webhook tests
   */
  static async runAllTests(therapistId: string): Promise<void> {
    console.log('🚀 Starting Comprehensive Webhook Tests');
    console.log('==========================================');
    console.log(`Therapist ID: ${therapistId}`);
    console.log('');

    try {
      // Test 1: Webhook Setup
      await this.testWebhookSetup(therapistId);
      console.log('');

      // Test 2: Comprehensive Sync
      await this.testComprehensiveSync(therapistId);
      console.log('');

      // Test 3: Detached Series Detection
      await this.testDetachedSeriesDetection();
      console.log('');

      // Test 4: Webhook Notification Processing
      await this.testWebhookNotification(therapistId);
      console.log('');

      // Test 5: Deleted Event Handling
      await this.testDeletedEventHandling();
      console.log('');

      // Test 6: Run WebhookTestService tests
      await WebhookTestService.runAllTests(therapistId);
      console.log('');

      console.log('🎉 All webhook tests completed successfully!');
      console.log('==========================================');

    } catch (error: any) {
      console.error('❌ Webhook tests failed:', error.message);
      console.log('==========================================');
    }
  }

  /**
   * Test webhook renewal functionality
   */
  static async testWebhookRenewal(channelId: string): Promise<void> {
    console.log('🔄 Testing Webhook Renewal...');
    
    try {
      const renewalSuccess = await CalendarWebhookService.renewWebhook(channelId);
      
      if (renewalSuccess) {
        console.log('✅ Webhook renewal successful');
      } else {
        console.log('⚠️ Webhook renewal failed');
      }

    } catch (error: any) {
      console.error('❌ Error testing webhook renewal:', error.message);
    }
  }

  /**
   * Monitor webhook status
   */
  static async monitorWebhookStatus(): Promise<void> {
    console.log('📊 Monitoring Webhook Status...');
    
    try {
      const activeWebhooks = await CalendarWebhookService.getActiveWebhooks();
      
      console.log(`📈 Active Webhooks: ${activeWebhooks.length}`);
      
      for (const webhook of activeWebhooks) {
        const expirationDate = new Date(parseInt(webhook.expiration));
        const timeUntilExpiry = expirationDate.getTime() - Date.now();
        const hoursUntilExpiry = Math.round(timeUntilExpiry / (1000 * 60 * 60));
        
        console.log(`- Therapist: ${webhook.therapistId}`);
        console.log(`  Channel: ${webhook.channelId}`);
        console.log(`  Expires: ${expirationDate.toISOString()} (${hoursUntilExpiry}h)`);
        console.log(`  Syncs: ${webhook.syncStatistics?.totalSyncs || 0}`);
        console.log('');
      }

    } catch (error: any) {
      console.error('❌ Error monitoring webhook status:', error.message);
    }
  }
}

// Export for use in other files
export default WebhookSyncTester;

// If running directly
if (require.main === module) {
  const therapistId = process.argv[2] || 'test-therapist-id';
  
  console.log('Starting Webhook Sync Tests...');
  WebhookSyncTester.runAllTests(therapistId)
    .then(() => {
      console.log('Tests completed.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Tests failed:', error);
      process.exit(1);
    });
}
