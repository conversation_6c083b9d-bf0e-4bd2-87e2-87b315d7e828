/**
 * Webhook Test Script
 * 
 * This script tests the enhanced webhook functionality for Google Calendar sync
 * including detection of deleted events, series rescheduling, and detached IDs
 */

import { CalendarWebhookService } from '../services/calendarWebhook.service';
import { EnhancedRescheduleDetectorService } from '../services/enhancedRescheduleDetector.service';

export class WebhookTestService {
  
  /**
   * Test webhook notification processing
   */
  static async testWebhookNotification(therapistId: string): Promise<void> {
    console.log('=== Testing Webhook Notification Processing ===');
    
    // Simulate webhook headers from Google Calendar
    const mockHeaders = {
      'x-goog-channel-id': `therapist_${therapistId}_${Date.now()}`,
      'x-goog-resource-state': 'exists',
      'x-goog-resource-id': 'test-resource-id',
      'x-goog-resource-uri': 'https://www.googleapis.com/calendar/v3/calendars/primary/events',
      'x-goog-channel-token': therapistId,
      'x-goog-event-type': 'update',
      'x-goog-event-id': 'test-event-id'
    };

    try {
      const result = await CalendarWebhookService.processWebhookNotification(mockHeaders);
      console.log('Webhook processing result:', result);
    } catch (error: any) {
      console.error('Error testing webhook notification:', error.message);
    }
  }

  /**
   * Test detached series event detection
   */
  static async testDetachedSeriesDetection(): Promise<void> {
    console.log('=== Testing Detached Series Event Detection ===');
    
    // Mock Google Calendar events with detached IDs
    const mockGoogleEvents = [
      {
        id: 'e3dmdlbilb9rjf3hb9qaicf75_20250708T064500Z', // Detached ID
        summary: 'Therapy Session with John Doe',
        start: { dateTime: '2025-07-08T06:45:00Z' },
        end: { dateTime: '2025-07-08T07:45:00Z' },
        attendees: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ],
        iCalUID: '<EMAIL>',
        updated: new Date().toISOString()
      },
      {
        id: 'abc123def456_20250709T064500Z', // Another detached ID
        summary: 'Therapy Session with Jane Smith',
        start: { dateTime: '2025-07-09T06:45:00Z' },
        end: { dateTime: '2025-07-09T07:45:00Z' },
        attendees: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ],
        iCalUID: '<EMAIL>',
        updated: new Date().toISOString(),
        recurrence: ['RRULE:FREQ=WEEKLY;COUNT=10'] // Indicates entire series
      }
    ];

    try {
      const detectionResult = await EnhancedRescheduleDetectorService.detectAdvancedReschedules(
        'test-therapist-id',
        mockGoogleEvents
      );

      console.log('Detection Results:');
      console.log('- New Events:', detectionResult.newEvents.length);
      console.log('- Rescheduled Events:', detectionResult.rescheduledEvents.length);
      console.log('- Deleted Events:', detectionResult.deletedEvents.length);
      console.log('- Series Changes:', detectionResult.seriesChanges.length);
      
      // Log series changes details
      detectionResult.seriesChanges.forEach((change, index) => {
        console.log(`Series Change ${index + 1}:`, {
          seriesId: change.seriesId,
          changeType: change.changeType,
          isDetachedSeries: change.isDetachedSeries,
          originalEventId: change.originalEventId,
          affectedInstances: change.affectedInstances
        });
      });

    } catch (error: any) {
      console.error('Error testing detached series detection:', error.message);
    }
  }

  /**
   * Test comprehensive sync functionality
   */
  static async testComprehensiveSync(therapistId: string): Promise<void> {
    console.log('=== Testing Comprehensive Sync ===');
    
    try {
      // This would test the comprehensive sync functionality
      console.log(`Testing comprehensive sync for therapist: ${therapistId}`);
      
      // Mock the sync process
      const mockSyncResult = {
        success: true,
        syncedEvents: 15,
        updatedEvents: 3,
        createdEvents: 2,
        deletedEvents: 1,
        errors: [],
        conflicts: []
      };

      console.log('Mock Sync Result:', mockSyncResult);
      
      // Test handling of different change types
      if (mockSyncResult.deletedEvents > 0) {
        console.log('✓ Deleted events detected and would be processed');
      }
      
      if (mockSyncResult.updatedEvents > 0) {
        console.log('✓ Updated events detected and would be processed');
      }
      
      if (mockSyncResult.createdEvents > 0) {
        console.log('✓ New events detected and would be processed');
      }

    } catch (error: any) {
      console.error('Error testing comprehensive sync:', error.message);
    }
  }

  /**
   * Test deleted event handling
   */
  static async testDeletedEventHandling(): Promise<void> {
    console.log('=== Testing Deleted Event Handling ===');
    
    // Mock deleted events
    const mockDeletedEvents = [
      {
        id: 'deleted-event-1',
        summary: 'Cancelled Session',
        scheduleId: 'mock-schedule-id-1',
        _id: 'mock-calendar-event-id-1'
      },
      {
        id: 'deleted-event-2',
        summary: 'Cancelled Series',
        scheduleId: 'mock-schedule-id-2',
        _id: 'mock-calendar-event-id-2'
      }
    ];

    try {
      console.log(`Processing ${mockDeletedEvents.length} deleted events...`);
      
      // This would call the actual deletion handling
      for (const deletedEvent of mockDeletedEvents) {
        console.log(`- Processing deleted event: ${deletedEvent.id} (${deletedEvent.summary})`);
        // The actual implementation would mark sessions as cancelled
      }
      
      console.log('✓ All deleted events processed successfully');

    } catch (error: any) {
      console.error('Error testing deleted event handling:', error.message);
    }
  }

  /**
   * Run all webhook tests
   */
  static async runAllTests(therapistId: string): Promise<void> {
    console.log('🚀 Starting Comprehensive Webhook Tests');
    console.log('==========================================');
    
    try {
      await this.testWebhookNotification(therapistId);
      console.log('');
      
      await this.testDetachedSeriesDetection();
      console.log('');
      
      await this.testComprehensiveSync(therapistId);
      console.log('');
      
      await this.testDeletedEventHandling();
      console.log('');
      
      console.log('✅ All webhook tests completed successfully!');
      
    } catch (error: any) {
      console.error('❌ Webhook tests failed:', error.message);
    }
  }

  /**
   * Test webhook setup and status
   */
  static async testWebhookSetup(therapistId: string): Promise<void> {
    console.log('=== Testing Webhook Setup ===');
    
    try {
      // Test webhook setup
      console.log(`Setting up webhook for therapist: ${therapistId}`);
      const webhook = await CalendarWebhookService.setupWebhook(therapistId);
      
      if (webhook) {
        console.log('✓ Webhook setup successful:', {
          channelId: webhook.id,
          expiration: webhook.expiration
        });
        
        // Test webhook status
        const activeWebhooks = await CalendarWebhookService.getActiveWebhooks();
        console.log(`✓ Active webhooks count: ${activeWebhooks.length}`);
        
      } else {
        console.log('❌ Webhook setup failed');
      }
      
    } catch (error: any) {
      console.error('Error testing webhook setup:', error.message);
    }
  }
}

// Export for use in other files
export default WebhookTestService;
