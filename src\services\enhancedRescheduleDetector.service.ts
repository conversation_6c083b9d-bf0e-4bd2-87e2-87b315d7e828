/**
 * Enhanced Reschedule Detector Service
 * 
 * This service provides advanced reschedule detection and handling:
 * - Detects all types of rescheduling (time changes, date changes, series changes)
 * - Handles recurring event modifications
 * - Provides conflict resolution strategies
 * - Supports automatic updates
 */

import moment from "moment-timezone";
import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { ScheduleStatus } from "../models/Schedule.model";

export interface IRescheduleDetectionResult {
  newEvents: any[];
  rescheduledEvents: any[];
  deletedEvents: any[];
  conflictingEvents: any[];
  seriesChanges: ISeriesChange[];
}

export interface ISeriesChange {
  seriesId: string;
  changeType: 'single_instance' | 'partial_reschedule' | 'entire_series' | 'partial_series';
  originalPattern: string;
  newPattern: string;
  affectedInstances: string[];
  affectedEvents?: any[];
  isDetachedSeries?: boolean;
  originalEventId?: string;
}

export interface IRescheduleConflict {
  eventId: string;
  conflictType: 'time_overlap' | 'double_booking' | 'series_conflict';
  conflictingWith: string[];
  suggestedResolution: string;
}

export class EnhancedRescheduleDetectorService {
  
  /**
   * Comprehensive reschedule detection with advanced pattern recognition
   */
  static async detectAdvancedReschedules(
    therapistId: string,
    googleEvents: any[]
  ): Promise<IRescheduleDetectionResult> {
    const result: IRescheduleDetectionResult = {
      newEvents: [],
      rescheduledEvents: [],
      deletedEvents: [],
      conflictingEvents: [],
      seriesChanges: []
    };

    // Get all existing events for this therapist
    const existingEvents = await CalendarEventDao.getTherapistCalendarEvents(therapistId);
    const existingEventMap = new Map(existingEvents.map(e => [e.id, e]));

    // Group events by series (using iCalUID)
    const googleEventSeries = this.groupEventsBySeries(googleEvents);
    const existingEventSeries = this.groupEventsBySeries(existingEvents);

    // Process each Google Calendar event
    for (const googleEvent of googleEvents) {
      const existingEvent = existingEventMap.get(googleEvent.id);

      if (existingEvent) {
        // Check for modifications to existing events
        const modifications = this.detectEventModifications(existingEvent, googleEvent);
        if (modifications.length > 0) {
          result.rescheduledEvents.push({
            ...googleEvent,
            existingEvent,
            modifications,
            changeType: this.categorizeChange(modifications)
          });
        }
      } else {
        // Check if this is a detached series event (rescheduled from Google Calendar)
        const detachedSeriesChange = this.detectDetachedSeriesEvent(googleEvent, existingEventSeries);
        if (detachedSeriesChange) {
          result.seriesChanges.push(detachedSeriesChange);
        } else {
          // Check if this is a new instance of an existing series
          const seriesChange = this.detectSeriesChange(googleEvent, existingEventSeries);
          if (seriesChange) {
            result.seriesChanges.push(seriesChange);
          } else {
            // Truly new event
            result.newEvents.push(googleEvent);
          }
        }
      }
    }

    // Detect deleted events (exist in database but not in Google Calendar)
    const googleEventIds = new Set(googleEvents.map(e => e.id));
    for (const existingEvent of existingEvents) {
      if (!googleEventIds.has(existingEvent.id)) {
        result.deletedEvents.push(existingEvent);
      }
    }

    // Detect conflicts
    const conflicts = await this.detectConflicts(result.rescheduledEvents, therapistId);
    result.conflictingEvents = conflicts.map(c => c.eventId);

    return result;
  }

  /**
   * Automatically handle detected reschedules
   */
  static async autoHandleReschedules(
    therapistId: string,
    detectionResult: IRescheduleDetectionResult
  ): Promise<{
    successful: number;
    failed: number;
    conflicts: number;
    errors: any[];
  }> {
    const result = {
      successful: 0,
      failed: 0,
      conflicts: 0,
      errors: [] as any[]
    };

    // Handle rescheduled events
    for (const rescheduledEvent of detectionResult.rescheduledEvents) {
      try {
        const success = await this.updateRescheduledEvent(therapistId, rescheduledEvent);
        if (success) {
          result.successful++;
        } else {
          result.failed++;
        }
      } catch (error: any) {
        result.failed++;
        result.errors.push({
          eventId: rescheduledEvent.id,
          error: error.message
        });
      }
    }

    // Handle series changes
    for (const seriesChange of detectionResult.seriesChanges) {
      try {
        const success = await this.handleSeriesChange(therapistId, seriesChange);
        if (success) {
          result.successful++;
        } else {
          result.failed++;
        }
      } catch (error: any) {
        result.failed++;
        result.errors.push({
          seriesId: seriesChange.seriesId,
          error: error.message
        });
      }
    }

    // Handle conflicts
    result.conflicts = detectionResult.conflictingEvents.length;

    return result;
  }

  /**
   * Group events by series using iCalUID
   */
  private static groupEventsBySeries(events: any[]): Map<string, any[]> {
    const series = new Map<string, any[]>();
    
    for (const event of events) {
      const seriesId = this.extractSeriesId(event);
      if (!series.has(seriesId)) {
        series.set(seriesId, []);
      }
      series.get(seriesId)!.push(event);
    }
    
    return series;
  }

  /**
   * Extract series ID from event (base iCalUID without instance suffix)
   */
  private static extractSeriesId(event: any): string {
    const iCalUID = event.iCalUID || event.id;
    // Remove instance-specific suffix (e.g., _20231215T100000Z)
    return iCalUID.split('_')[0];
  }

  /**
   * Detect modifications between existing and Google Calendar event
   */
  private static detectEventModifications(existingEvent: any, googleEvent: any): string[] {
    const modifications: string[] = [];

    // Check time changes
    const existingStart = new Date(existingEvent.start.dateTime).getTime();
    const googleStart = new Date(googleEvent.start.dateTime).getTime();
    const existingEnd = new Date(existingEvent.end.dateTime).getTime();
    const googleEnd = new Date(googleEvent.end.dateTime).getTime();

    if (existingStart !== googleStart || existingEnd !== googleEnd) {
      modifications.push('time_change');
    }

    // Check summary changes
    if (existingEvent.summary !== googleEvent.summary) {
      modifications.push('summary_change');
    }

    // Check attendee changes
    const existingAttendees = (existingEvent.attendees || []).map((a: any) => a.email).sort();
    const googleAttendees = (googleEvent.attendees || []).map((a: any) => a.email).sort();
    
    if (JSON.stringify(existingAttendees) !== JSON.stringify(googleAttendees)) {
      modifications.push('attendee_change');
    }

    // Check location changes
    if (existingEvent.location !== googleEvent.location) {
      modifications.push('location_change');
    }

    return modifications;
  }

  /**
   * Categorize the type of change
   */
  private static categorizeChange(modifications: string[]): string {
    if (modifications.includes('time_change')) {
      return 'reschedule';
    }
    if (modifications.includes('attendee_change')) {
      return 'attendee_update';
    }
    if (modifications.includes('summary_change') || modifications.includes('location_change')) {
      return 'details_update';
    }
    return 'minor_update';
  }

  /**
   * Detect detached series events (events with new IDs created by Google Calendar rescheduling)
   */
  private static detectDetachedSeriesEvent(
    googleEvent: any,
    existingEventSeries: Map<string, any[]>
  ): ISeriesChange | null {
    // Check if this event has a detached ID pattern (contains underscore with timestamp)
    const detachedIdPattern = /^[a-zA-Z0-9]+_\d{8}T\d{6}Z$/;
    if (!detachedIdPattern.test(googleEvent.id)) {
      return null;
    }

    // Extract the base ID (before the underscore)
    const baseId = googleEvent.id.split('_')[0];

    // Look for existing events with similar base ID or iCalUID
    const seriesEntries = Array.from(existingEventSeries.entries());
    for (const [seriesId, events] of seriesEntries) {
      for (const existingEvent of events) {
        // Check if this could be a rescheduled version of an existing event
        if (this.isLikelyRescheduledEvent(googleEvent, existingEvent)) {
          // Determine if this is entire series or partial series reschedule
          const rescheduleType = this.determineRescheduleType(googleEvent, events);

          return {
            seriesId: baseId,
            changeType: rescheduleType,
            originalPattern: 'existing',
            newPattern: 'detached',
            affectedInstances: [googleEvent.id],
            affectedEvents: [googleEvent],
            isDetachedSeries: true,
            originalEventId: existingEvent.id
          };
        }
      }
    }

    return null;
  }

  /**
   * Check if a Google Calendar event is likely a rescheduled version of an existing event
   */
  private static isLikelyRescheduledEvent(googleEvent: any, existingEvent: any): boolean {
    // Compare attendees (should be the same for rescheduled events)
    const googleAttendees = (googleEvent.attendees || []).map((a: any) => a.email).sort();
    const existingAttendees = (existingEvent.attendees || []).map((a: any) => a.email).sort();

    // If attendees match exactly, it's likely the same session
    const attendeesMatch = JSON.stringify(googleAttendees) === JSON.stringify(existingAttendees);

    // Compare summary (should be similar)
    let summaryMatch = true;
    if (googleEvent.summary && existingEvent.summary) {
      summaryMatch = googleEvent.summary.toLowerCase().includes(existingEvent.summary.toLowerCase()) ||
                    existingEvent.summary.toLowerCase().includes(googleEvent.summary.toLowerCase());
    }

    // For detached series, we're more lenient - if attendees match and summary is similar, it's likely rescheduled
    if (attendeesMatch && summaryMatch) {
      return true;
    }

    // Additional check: if the base ID matches (for detached events)
    if (googleEvent.id.includes('_') && existingEvent.id) {
      const googleBaseId = googleEvent.id.split('_')[0];
      if (existingEvent.id.startsWith(googleBaseId) || existingEvent.iCalUID?.includes(googleBaseId)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Determine if this is an entire series or partial series reschedule
   */
  private static determineRescheduleType(googleEvent: any, existingEvents: any[]): 'entire_series' | 'partial_series' | 'single_instance' {
    // For detached events, we need to analyze the pattern
    // If the event has recurrence rules, it's likely entire series
    if (googleEvent.recurrence && googleEvent.recurrence.length > 0) {
      return 'entire_series';
    }

    // If there are multiple existing events in the series, it could be partial
    if (existingEvents.length > 1) {
      return 'partial_series';
    }

    return 'single_instance';
  }

  /**
   * Detect series-level changes
   */
  private static detectSeriesChange(
    googleEvent: any,
    existingEventSeries: Map<string, any[]>
  ): ISeriesChange | null {
    const seriesId = this.extractSeriesId(googleEvent);
    const existingSeries = existingEventSeries.get(seriesId);

    if (!existingSeries || existingSeries.length === 0) {
      return null;
    }

    // Check if this is a new instance in an existing series
    const isNewInstance = !existingSeries.some(e => e.id === googleEvent.id);

    if (isNewInstance) {
      // Check if this is part of a series reschedule by looking at the originalStartTime
      if (googleEvent.originalStartTime) {
        // This indicates a single instance override in a recurring series
        return {
          seriesId,
          changeType: 'single_instance',
          originalPattern: 'existing',
          newPattern: 'modified',
          affectedInstances: [googleEvent.id],
          affectedEvents: [googleEvent]
        };
      }

      // Check if multiple events in the series have been modified
      // This could indicate a partial or entire series reschedule
      const modifiedEvents = this.findModifiedEventsInSeries(googleEvent, existingSeries);

      if (modifiedEvents.length > 1) {
        // Determine if it's partial or entire series based on the pattern
        const changeType = this.determineSeriesChangeType(modifiedEvents, existingSeries);

        return {
          seriesId,
          changeType,
          originalPattern: 'existing',
          newPattern: 'modified',
          affectedInstances: modifiedEvents.map(e => e.id),
          affectedEvents: modifiedEvents
        };
      }

      return {
        seriesId,
        changeType: 'single_instance',
        originalPattern: 'existing',
        newPattern: 'modified',
        affectedInstances: [googleEvent.id],
        affectedEvents: [googleEvent]
      };
    }

    return null;
  }

  /**
   * Find modified events in a series
   */
  private static findModifiedEventsInSeries(googleEvent: any, existingSeries: any[]): any[] {
    // This is a simplified implementation
    // In a real scenario, you'd need to compare timestamps and detect patterns
    return [googleEvent];
  }

  /**
   * Determine if series change is partial or entire
   */
  private static determineSeriesChangeType(
    modifiedEvents: any[],
    existingSeries: any[]
  ): 'partial_reschedule' | 'entire_series' {
    // If more than 50% of events are modified, consider it entire series
    const modificationRatio = modifiedEvents.length / existingSeries.length;
    return modificationRatio > 0.5 ? 'entire_series' : 'partial_reschedule';
  }

  /**
   * Update a rescheduled event in the database
   */
  private static async updateRescheduledEvent(
    therapistId: string,
    rescheduledEvent: any
  ): Promise<boolean> {
    try {
      const calendarEvent = await CalendarEventDao.findByGoogleEventId(rescheduledEvent.id);
      if (!calendarEvent) {
        return false;
      }

      const schedule = await ScheduleDao.getScheduleById(calendarEvent.scheduleId);
      if (!schedule) {
        return false;
      }

      // Find the corresponding recurrence date
      const recurrenceDate = schedule.recurrenceDates.find(
        (rd: any) => rd.calenderEventId?.toString() === calendarEvent._id.toString()
      );

      if (!recurrenceDate) {
        return false;
      }

      // Update times if this is a reschedule
      if (rescheduledEvent.modifications.includes('time_change')) {
        recurrenceDate.fromDate = new Date(rescheduledEvent.start.dateTime);
        recurrenceDate.toDate = new Date(rescheduledEvent.end.dateTime);
        recurrenceDate.status = ScheduleStatus.RESCHEDULED;
      }

      // Update calendar event
      calendarEvent.start = rescheduledEvent.start;
      calendarEvent.end = rescheduledEvent.end;
      calendarEvent.updated = new Date(rescheduledEvent.updated);
      calendarEvent.summary = rescheduledEvent.summary;

      // Save changes
      await schedule.save();
      await calendarEvent.save();

      return true;
    } catch (error) {
      console.error('Error updating rescheduled event:', error);
      return false;
    }
  }

  /**
   * Handle series-level changes
   */
  private static async handleSeriesChange(
    therapistId: string,
    seriesChange: ISeriesChange
  ): Promise<boolean> {
    try {
      // Implementation depends on the type of series change
      // This is a placeholder for more complex series handling logic
      console.log(`Handling series change: ${seriesChange.changeType} for series ${seriesChange.seriesId}`);
      return true;
    } catch (error) {
      console.error('Error handling series change:', error);
      return false;
    }
  }

  /**
   * Detect conflicts in rescheduled events
   */
  private static async detectConflicts(
    rescheduledEvents: any[],
    therapistId: string
  ): Promise<IRescheduleConflict[]> {
    const conflicts: IRescheduleConflict[] = [];

    // Get all existing schedules for conflict detection
    const existingSchedules = await ScheduleDao.getScheduleByTherapistId(therapistId);
    const existingTimeSlots: any[] = [];

    for (const schedule of existingSchedules) {
      for (const recDate of schedule.recurrenceDates) {
        if (recDate.status !== ScheduleStatus.CANCELLED) {
          existingTimeSlots.push({
            fromDate: moment(recDate.fromDate),
            toDate: moment(recDate.toDate),
            scheduleId: schedule._id,
            recurrenceId: recDate._id
          });
        }
      }
    }

    // Check each rescheduled event for conflicts
    for (const rescheduledEvent of rescheduledEvents) {
      if (!rescheduledEvent.modifications.includes('time_change')) {
        continue;
      }

      const newStart = moment(rescheduledEvent.start.dateTime);
      const newEnd = moment(rescheduledEvent.end.dateTime);

      const conflictingSlots = existingTimeSlots.filter(slot => {
        return newStart.isBefore(slot.toDate) && newEnd.isAfter(slot.fromDate);
      });

      if (conflictingSlots.length > 0) {
        conflicts.push({
          eventId: rescheduledEvent.id,
          conflictType: 'time_overlap',
          conflictingWith: conflictingSlots.map(slot => slot.scheduleId.toString()),
          suggestedResolution: 'Manual review required - overlapping appointments detected'
        });
      }
    }

    return conflicts;
  }
}
