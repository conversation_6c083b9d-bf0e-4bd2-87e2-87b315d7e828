import { Router } from "express";
import TherapistRouter from "./therapist.router";
import HookRouter from "./hook.router";
import AdminRouter from "./admin.router";
import ClientRouter from "./client.router";
import AuthRouter from "./auth.router";
import { adminAuthMiddleware } from "../../middleware/admin.auth.middleware";
import NotificationRouter from "./notication.router";
import ScriptRouter from "./script.router";
import payTrackerScriptRouter from "./scriptPayTracker.router";
import { CalendarWebhookRouter } from "./calendarWebhook.router";

export default class V1Router {
  public router: Router;

  constructor() {
    this.router = Router();
    this.routes();
  }

  public routes(): void {
    this.router.use("/therapist", new TherapistRouter().router);
    this.router.use("/hook", new HookRouter().router);
    this.router.use("/admin", adminAuthMiddleware(), new AdminRouter().router);
    this.router.use("/client", new ClientRouter().router);
    this.router.use("/auth", new AuthRouter().router);
    this.router.use("/notification", new NotificationRouter().router);
    this.router.use("/script", new ScriptRouter().router);
    this.router.use("/payTracker-script", new payTrackerScriptRouter().router);
    this.router.use("/webhook", new CalendarWebhookRouter().router);
  }
}
