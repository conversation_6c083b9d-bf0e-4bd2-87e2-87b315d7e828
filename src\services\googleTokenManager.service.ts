/**
 * Google Token Manager Service
 * 
 * Handles Google OAuth2 token management including:
 * - Automatic token refresh
 * - Token validation
 * - Error handling for expired tokens
 */

import { google } from "googleapis";
import { CONFIG } from "../config/environment";
import { GoogleCalendarService } from "./googleCalendar.service";

export interface ITokenRefreshResult {
  success: boolean;
  googleCalendarData?: any;
  error?: string;
}

export class GoogleTokenManagerService {
  
  /**
   * Get authenticated OAuth2 client with automatic token refresh setup
   */
  static async getAuthenticatedClient(googleCalendarData: any): Promise<any> {
    const oauth2Client = new google.auth.OAuth2(
      CONFIG.clientId,
      CONFIG.clientSecret,
      CONFIG.clientRedirectUrl
    );

    oauth2Client.setCredentials({
      access_token: googleCalendarData.access_token,
      refresh_token: googleCalendarData.refresh_token
    });

    // Set up automatic token refresh event handler
    oauth2Client.on('tokens', async (tokens) => {
      try {
        console.log(`🔄 Auto-refreshing tokens for therapist ${googleCalendarData.therapistId}`);
        
        // Update the database with new tokens
        await GoogleCalendarService.update(googleCalendarData._id, {
          access_token: tokens.access_token,
          refresh_token: tokens.refresh_token || googleCalendarData.refresh_token
        });
        
        console.log(`✅ Tokens auto-refreshed successfully for therapist ${googleCalendarData.therapistId}`);
      } catch (error: any) {
        console.error(`❌ Failed to update auto-refreshed tokens for therapist ${googleCalendarData.therapistId}:`, error.message);
      }
    });

    return oauth2Client;
  }

  /**
   * Manually refresh access token
   */
  static async refreshAccessToken(googleCalendarData: any): Promise<ITokenRefreshResult> {
    try {
      console.log(`🔄 Manually refreshing access token for therapist ${googleCalendarData.therapistId}`);
      
      if (!googleCalendarData.refresh_token) {
        throw new Error("No refresh token available");
      }

      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );

      oauth2Client.setCredentials({
        refresh_token: googleCalendarData.refresh_token
      });

      // Force token refresh
      const { credentials } = await oauth2Client.refreshAccessToken();
      
      if (!credentials.access_token) {
        throw new Error("Failed to obtain new access token");
      }

      // Update database with new tokens
      const updatedData = {
        access_token: credentials.access_token,
        refresh_token: credentials.refresh_token || googleCalendarData.refresh_token
      };

      await GoogleCalendarService.update(googleCalendarData._id, updatedData);

      console.log(`✅ Access token refreshed successfully for therapist ${googleCalendarData.therapistId}`);
      
      return {
        success: true,
        googleCalendarData: {
          ...googleCalendarData,
          ...updatedData
        }
      };
    } catch (error: any) {
      console.error(`❌ Failed to refresh access token for therapist ${googleCalendarData.therapistId}:`, error.message);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Check if error is related to token expiration
   */
  static isTokenExpiredError(error: any): boolean {
    if (!error) return false;

    const errorMessage = error.message?.toLowerCase() || '';
    const errorCode = error.code;
    const responseError = error.response?.data?.error;

    return (
      errorMessage.includes('invalid_grant') ||
      errorMessage.includes('token expired') ||
      errorMessage.includes('unauthorized') ||
      errorCode === 401 ||
      responseError === 'invalid_grant'
    );
  }

  /**
   * Validate if tokens are present and not obviously expired
   */
  static validateTokens(googleCalendarData: any): boolean {
    if (!googleCalendarData) {
      console.log('❌ No Google Calendar data provided');
      return false;
    }

    if (!googleCalendarData.access_token) {
      console.log(`❌ No access token for therapist ${googleCalendarData.therapistId}`);
      return false;
    }

    if (!googleCalendarData.refresh_token) {
      console.log(`❌ No refresh token for therapist ${googleCalendarData.therapistId}`);
      return false;
    }

    return true;
  }

  /**
   * Execute Google Calendar API call with automatic token refresh retry
   */
  static async executeWithTokenRefresh<T>(
    googleCalendarData: any,
    apiCall: (oauth2Client: any) => Promise<T>,
    maxRetries: number = 2
  ): Promise<T> {
    let currentData = googleCalendarData;
    let retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        // Validate tokens before attempting API call
        if (!this.validateTokens(currentData)) {
          throw new Error("Invalid or missing tokens");
        }

        const oauth2Client = await this.getAuthenticatedClient(currentData);
        return await apiCall(oauth2Client);

      } catch (error: any) {
        if (this.isTokenExpiredError(error) && retryCount < maxRetries) {
          console.log(`⚠️ Token expired for therapist ${currentData.therapistId}, attempting refresh (${retryCount + 1}/${maxRetries})`);
          
          const refreshResult = await this.refreshAccessToken(currentData);
          
          if (refreshResult.success && refreshResult.googleCalendarData) {
            currentData = refreshResult.googleCalendarData;
            retryCount++;
            continue; // Retry with refreshed token
          } else {
            throw new Error(`Token refresh failed: ${refreshResult.error}`);
          }
        } else {
          // Either not a token error, or max retries reached
          throw error;
        }
      }
    }

    throw new Error("Max retries exceeded");
  }

  /**
   * Bulk refresh tokens for multiple therapists
   */
  static async bulkRefreshTokens(therapistIds: string[]): Promise<{
    successful: string[];
    failed: Array<{ therapistId: string; error: string }>;
  }> {
    const result = {
      successful: [] as string[],
      failed: [] as Array<{ therapistId: string; error: string }>
    };

    console.log(`🔄 Starting bulk token refresh for ${therapistIds.length} therapists`);

    for (const therapistId of therapistIds) {
      try {
        const googleCalendarData = await GoogleCalendarService.findByTherapist(therapistId);
        
        if (!googleCalendarData) {
          result.failed.push({
            therapistId,
            error: "No Google Calendar data found"
          });
          continue;
        }

        const refreshResult = await this.refreshAccessToken(googleCalendarData);
        
        if (refreshResult.success) {
          result.successful.push(therapistId);
        } else {
          result.failed.push({
            therapistId,
            error: refreshResult.error || "Unknown error"
          });
        }
      } catch (error: any) {
        result.failed.push({
          therapistId,
          error: error.message
        });
      }
    }

    console.log(`✅ Bulk token refresh completed: ${result.successful.length} successful, ${result.failed.length} failed`);
    
    return result;
  }
}
