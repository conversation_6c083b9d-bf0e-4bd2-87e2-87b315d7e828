/**
 * Calendar Webhook Service
 *
 * This service handles Google Calendar webhooks for real-time synchronization:
 * - Sets up and manages webhook channels
 * - Processes incoming webhook notifications
 * - Triggers incremental sync on calendar changes
 * - Manages webhook lifecycle (creation, renewal, deletion)
 */

import { google } from "googleapis";
import { CONFIG } from "../config/environment";
import { GoogleCalendarService } from "./googleCalendar.service";
import { OptimizedCalendarSyncService } from "./optimizedCalendarSync.service";
import { EnhancedRescheduleDetectorService } from "./enhancedRescheduleDetector.service";
import CalendarWebhookModel from "../models/CalendarWebhook.model";
import moment from "moment";

export interface IWebhookChannel {
  id: string;
  resourceId: string;
  resourceUri: string;
  token?: string;
  expiration: number;
  therapistId: string;
  createdAt: Date;
  isActive: boolean;
}

export interface IWebhookNotification {
  channelId: string;
  resourceId: string;
  resourceState: 'exists' | 'not_exists' | 'sync';
  resourceUri: string;
  eventType?: string;
  eventId?: string;
}

export class CalendarWebhookService {
  private static readonly WEBHOOK_EXPIRATION_HOURS = 24 * 7; // 7 days
  private static readonly WEBHOOK_BASE_URL = CONFIG.webhookBaseUrl || 'https://your-domain.com';

  /**
   * Set up webhook for a therapist's calendar
   */
  static async setupWebhook(therapistId: string): Promise<IWebhookChannel | null> {
    try {
      const googleCalendarData = await GoogleCalendarService.findByTherapist(therapistId);
      if (!googleCalendarData) {
        throw new Error("No Google Calendar data found");
      }

      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );

      oauth2Client.setCredentials({
        access_token: googleCalendarData.access_token,
        refresh_token: googleCalendarData.refresh_token
      });

      const calendar = google.calendar({ version: "v3", auth: oauth2Client });

      // Generate unique channel ID
      const channelId = `therapist_${therapistId}_${Date.now()}`;
      const webhookUrl = `${this.WEBHOOK_BASE_URL}/api/v1/webhook/calendar`;

      // Set expiration time
      const expiration = moment().add(this.WEBHOOK_EXPIRATION_HOURS, 'hours').valueOf();

      // Create the watch request
      const watchRequest = {
        calendarId: 'primary',
        requestBody: {
          id: channelId,
          type: 'web_hook',
          address: webhookUrl,
          token: therapistId, // Use therapist ID as token for identification
          expiration: expiration.toString()
        }
      };

      const response = await calendar.events.watch(watchRequest);

      if (response.data) {
        const channel: IWebhookChannel = {
          id: channelId,
          resourceId: response.data.resourceId!,
          resourceUri: response.data.resourceUri!,
          token: therapistId,
          expiration: expiration,
          therapistId: therapistId,
          createdAt: new Date(),
          isActive: true
        };

        // Save to database instead of in-memory Map
        const webhookDoc = new CalendarWebhookModel({
          therapistId: therapistId,
          channelId: channelId,
          resourceId: response.data.resourceId!,
          resourceUri: response.data.resourceUri!,
          token: therapistId,
          expiration: new Date(expiration),
          isActive: true
        });

        await webhookDoc.save();

        console.log(`Webhook setup successful for therapist ${therapistId}`);
        return channel;
      }

      return null;
    } catch (error: any) {
      console.error(`Error setting up webhook for therapist ${therapistId}:`, error.message);
      return null;
    }
  }

  /**
   * Process incoming webhook notification
   */
  static async processWebhookNotification(
    headers: any,
    _body?: any
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Extract notification details from headers
      const channelId = headers['x-goog-channel-id'];
      const resourceState = headers['x-goog-resource-state'];
      const resourceId = headers['x-goog-resource-id'];
      const resourceUri = headers['x-goog-resource-uri'];
      const channelToken = headers['x-goog-channel-token'];

      console.log(`🔍 Processing webhook notification:`, {
        channelId,
        resourceState,
        resourceId,
        channelToken
      });

      if (!channelId || !resourceState) {
        console.error(`❌ Invalid webhook headers - missing channelId or resourceState`);
        return { success: false, message: "Invalid webhook headers" };
      }

      // Find the corresponding channel in database
      const channel = await CalendarWebhookModel.findOne({ channelId });
      if (!channel || !channel.isActive) {
        console.error(`❌ Channel not found or inactive:`, { channelId, found: !!channel, active: channel?.isActive });
        return { success: false, message: "Channel not found or inactive" };
      }

      // Verify token matches therapist ID
      if (channelToken !== channel.therapistId.toString()) {
        console.error(`❌ Token mismatch:`, {
          expected: channel.therapistId.toString(),
          received: channelToken
        });
        return { success: false, message: "Token mismatch" };
      }

      // Record the notification receipt
      channel.recordNotification();
      await channel.save();

      console.log(`✅ Webhook notification validated for therapist ${channel.therapistId}`);

      const notification: IWebhookNotification = {
        channelId,
        resourceId,
        resourceState: resourceState as any,
        resourceUri,
        eventType: headers['x-goog-event-type'],
        eventId: headers['x-goog-event-id']
      };

      // Process the notification based on resource state
      await this.handleNotification(notification, channel.therapistId.toString());

      return { success: true, message: "Notification processed successfully" };
    } catch (error: any) {
      console.error(`💥 Error processing webhook notification:`, error.message);
      console.error(`Stack trace:`, error.stack);
      return { success: false, message: error.message };
    }
  }

  /**
   * Handle specific notification types
   */
  private static async handleNotification(
    notification: IWebhookNotification,
    therapistId: string
  ): Promise<void> {
    console.log(`🔄 Handling notification type '${notification.resourceState}' for therapist ${therapistId}`);

    switch (notification.resourceState) {
      case 'exists':
        // Calendar has been modified - this is the key notification for series rescheduling
        console.log(`📅 Calendar modification detected for therapist ${therapistId}`);
        await this.handleCalendarChange(therapistId);
        break;

      case 'sync':
        // Initial sync notification (can be ignored or used for verification)
        console.log(`🔄 Sync notification received for therapist ${therapistId} - webhook setup confirmed`);
        break;

      case 'not_exists':
        // Resource no longer exists (calendar deleted)
        console.log(`🗑️ Calendar no longer exists for therapist ${therapistId}`);
        break;

      default:
        console.log(`❓ Unknown resource state: ${notification.resourceState} for therapist ${therapistId}`);
    }
  }

  /**
   * Handle calendar change notification using improved webhook sync logic
   */
  private static async handleCalendarChange(therapistId: string): Promise<void> {
    try {
      console.log(`🔄 Processing real-time calendar change for therapist ${therapistId}`);

      // Use the improved webhook sync service for better series detection
      const { ImprovedWebhookSyncService } = require('./improvedWebhookSync.service');
      const syncResult = await ImprovedWebhookSyncService.processWebhookChange(therapistId);

      if (syncResult.success) {
        console.log(`✅ Real-time webhook sync completed for therapist ${therapistId}:`, {
          processed: syncResult.processed,
          created: syncResult.created,
          updated: syncResult.updated,
          deleted: syncResult.deleted,
          seriesRescheduled: syncResult.seriesRescheduled,
          errors: syncResult.errors.length
        });

        // Update webhook statistics
        await CalendarWebhookModel.updateMany(
          { therapistId, isActive: true },
          {
            $inc: {
              'syncStatistics.totalSyncs': 1,
              'syncStatistics.successfulSyncs': 1,
              'syncStatistics.eventsProcessed': syncResult.processed
            },
            $set: { 'syncStatistics.lastSyncAt': new Date() }
          }
        );

        // Log series rescheduling specifically
        if (syncResult.seriesRescheduled > 0) {
          console.log(`🔄 Successfully processed ${syncResult.seriesRescheduled} series reschedule(s) via webhook`);
        }

      } else {
        console.error(`❌ Real-time webhook sync failed for therapist ${therapistId}:`, syncResult.errors);

        // Update webhook statistics for failed sync
        await CalendarWebhookModel.updateMany(
          { therapistId, isActive: true },
          {
            $inc: {
              'syncStatistics.totalSyncs': 1,
              'syncStatistics.failedSyncs': 1
            },
            $set: { 'syncStatistics.lastSyncAt': new Date() }
          }
        );
      }
    } catch (error: any) {
      console.error(`💥 Error handling calendar change for therapist ${therapistId}:`, error.message);
      console.error(`Stack trace:`, error.stack);

      // Update failed sync statistics
      try {
        await CalendarWebhookModel.updateMany(
          { therapistId, isActive: true },
          {
            $inc: {
              'syncStatistics.totalSyncs': 1,
              'syncStatistics.failedSyncs': 1
            },
            $set: { 'syncStatistics.lastSyncAt': new Date() }
          }
        );
      } catch (dbError: any) {
        console.error(`Error updating webhook statistics:`, dbError.message);
      }
    }
  }

  /**
   * Perform comprehensive sync to detect all changes including deletions
   */
  private static async performComprehensiveSync(therapistId: string): Promise<any> {
    try {
      // Get current Google Calendar events
      const googleEvents = await this.getAllCurrentEvents(therapistId);

      // Get existing database events
      const CalendarEventDao = require('../lib/dao/calendarEvent.dao').CalendarEventDao;
      const existingEvents = await CalendarEventDao.getTherapistCalendarEvents(therapistId);

      const result = {
        success: true,
        syncedEvents: 0,
        updatedEvents: 0,
        createdEvents: 0,
        deletedEvents: 0,
        errors: [] as any[],
        conflicts: [] as any[]
      };

      // Create maps for efficient lookup
      const googleEventMap = new Map(googleEvents.map((e: any) => [e.id, e]));
      const existingEventMap = new Map(existingEvents.map((e: any) => [e.id, e]));

      // Process existing events to find deleted ones
      for (const existingEvent of existingEvents) {
        if (!googleEventMap.has(existingEvent.id)) {
          // Event was deleted from Google Calendar
          await this.markEventAsDeleted(therapistId, existingEvent);
          result.deletedEvents++;
        }
      }

      // Process Google Calendar events
      for (const googleEvent of googleEvents) {
        const existingEvent = existingEventMap.get(googleEvent.id);

        if (existingEvent) {
          // Check for updates
          const eventUpdated = new Date(googleEvent.updated);
          const existingUpdated = new Date((existingEvent as any).updated);

          if (eventUpdated > existingUpdated) {
            result.updatedEvents++;
          }
        } else {
          // New event
          if (googleEvent.attendees && googleEvent.attendees.length > 0) {
            result.createdEvents++;
          }
        }
        result.syncedEvents++;
      }

      return result;
    } catch (error: any) {
      console.error(`Error in comprehensive sync for therapist ${therapistId}:`, error.message);
      return {
        success: false,
        syncedEvents: 0,
        updatedEvents: 0,
        createdEvents: 0,
        deletedEvents: 0,
        errors: [error.message],
        conflicts: []
      };
    }
  }

  /**
   * Get all current events from Google Calendar
   */
  private static async getAllCurrentEvents(therapistId: string): Promise<any[]> {
    try {
      const GoogleCalendarService = require('./googleCalendar.service').GoogleCalendarService;

      // Get events from the last month to next 6 months for comprehensive analysis
      const timeMin = new Date();
      timeMin.setMonth(timeMin.getMonth() - 1);

      const timeMax = new Date();
      timeMax.setMonth(timeMax.getMonth() + 6);

      const events = await GoogleCalendarService.eventByDate(
        therapistId,
        500, // Higher limit for comprehensive sync
        timeMin,
        timeMax
      );

      return events || [];
    } catch (error: any) {
      console.error(`Error getting all current events for therapist ${therapistId}:`, error.message);
      return [];
    }
  }

  /**
   * Handle series reschedules detected from Google Calendar
   */
  private static async handleSeriesReschedules(
    therapistId: string,
    seriesChanges: any[]
  ): Promise<void> {
    for (const seriesChange of seriesChanges) {
      try {
        console.log(`Processing series reschedule for therapist ${therapistId}:`, {
          seriesId: seriesChange.seriesId,
          changeType: seriesChange.changeType,
          affectedEvents: seriesChange.affectedEvents?.length || 0
        });

        // Determine reschedule type based on the series change
        let rescheduleType = 'entire_series';
        if (seriesChange.changeType === 'partial_reschedule') {
          rescheduleType = 'partial_series';
        } else if (seriesChange.changeType === 'single_instance') {
          rescheduleType = 'single_session';
        }

        // Find the first affected event to get timing information
        const firstAffectedEvent = seriesChange.affectedEvents?.[0];
        if (!firstAffectedEvent) {
          console.warn(`No affected events found for series change: ${seriesChange.seriesId}`);
          continue;
        }

        // Call the reschedule-calendar-series endpoint
        const axios = require('axios');
        const baseUrl = process.env.API_BASE_URL || 'http://localhost:3000';

        const rescheduleData = {
          calendarSeriesId: seriesChange.seriesId,
          calendarEventId: firstAffectedEvent.id,
          newStartTime: firstAffectedEvent.start.dateTime,
          newEndTime: firstAffectedEvent.end.dateTime,
          rescheduleType
        };

        console.log(`Calling reschedule-calendar-series endpoint with data:`, rescheduleData);

        // Make internal API call to handle the reschedule
        const response = await axios.put(
          `${baseUrl}/api/v1/therapist/schedule/reschedule-calendar-series`,
          rescheduleData,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${await this.getTherapistToken(therapistId)}`
            }
          }
        );

        if (response.data.success) {
          console.log(`Successfully processed series reschedule for ${seriesChange.seriesId}`);
        } else {
          console.error(`Failed to process series reschedule:`, response.data.message);
        }

      } catch (error: any) {
        console.error(`Error handling series reschedule for ${seriesChange.seriesId}:`, error.message);
      }
    }
  }

  /**
   * Get therapist authentication token for internal API calls
   */
  private static async getTherapistToken(therapistId: string): Promise<string> {
    // This is a simplified approach - in production you might want to use
    // a service account or internal authentication mechanism
    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      { _id: therapistId, type: 'therapist' },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '1h' }
    );
    return token;
  }

  /**
   * Renew webhook before expiration
   */
  static async renewWebhook(channelId: string): Promise<boolean> {
    try {
      const channel = await CalendarWebhookModel.findOne({ channelId });
      if (!channel) {
        console.error(`Webhook channel ${channelId} not found for renewal`);
        return false;
      }

      const therapistId = channel.therapistId.toString();
      console.log(`Renewing webhook for therapist ${therapistId}, old channel: ${channelId}`);

      // Stop the old webhook first
      const stopResult = await this.stopWebhook(channelId);
      if (!stopResult) {
        console.warn(`Failed to stop old webhook ${channelId}, proceeding with renewal anyway`);
      }

      // Mark old webhook as inactive in database
      await CalendarWebhookModel.updateOne(
        { channelId },
        { $set: { isActive: false } }
      );

      // Create a new webhook
      const newChannel = await this.setupWebhook(therapistId);

      if (newChannel) {
        console.log(`Webhook renewal successful for therapist ${therapistId}. New channel: ${newChannel.id}`);
        return true;
      } else {
        console.error(`Failed to create new webhook for therapist ${therapistId}`);
        return false;
      }
    } catch (error: any) {
      console.error(`Error renewing webhook ${channelId}:`, error.message);
      return false;
    }
  }

  /**
   * Stop webhook channel
   */
  static async stopWebhook(channelId: string): Promise<boolean> {
    try {
      // Find webhook in database
      const webhook = await CalendarWebhookModel.findOne({ channelId });
      if (!webhook) {
        console.warn(`Webhook ${channelId} not found in database`);
        return false;
      }

      const googleCalendarData = await GoogleCalendarService.findByTherapist(webhook.therapistId);
      if (!googleCalendarData) {
        console.warn(`Google Calendar data not found for therapist ${webhook.therapistId}`);
        // Still mark as inactive in database even if we can't stop it in Google
        webhook.isActive = false;
        await webhook.save();
        return false;
      }

      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );

      oauth2Client.setCredentials({
        access_token: googleCalendarData.access_token,
        refresh_token: googleCalendarData.refresh_token
      });

      const calendar = google.calendar({ version: "v3", auth: oauth2Client });

      try {
        // Stop the channel in Google Calendar
        await calendar.channels.stop({
          requestBody: {
            id: webhook.channelId,
            resourceId: webhook.resourceId
          }
        });
        console.log(`Successfully stopped webhook channel ${channelId} in Google Calendar`);
      } catch (googleError: any) {
        console.warn(`Failed to stop webhook ${channelId} in Google Calendar: ${googleError.message}`);
        // Continue to mark as inactive in database even if Google API call fails
      }

      // Update database - mark as inactive
      webhook.isActive = false;
      await webhook.save();

      console.log(`Webhook ${channelId} marked as inactive in database`);
      return true;
    } catch (error: any) {
      console.error(`Error stopping webhook ${channelId}:`, error.message);

      // Try to mark as inactive in database as fallback
      try {
        await CalendarWebhookModel.updateOne(
          { channelId },
          { $set: { isActive: false } }
        );
        console.log(`Fallback: marked webhook ${channelId} as inactive`);
      } catch (dbError: any) {
        console.error(`Failed to mark webhook ${channelId} as inactive: ${dbError.message}`);
      }

      return false;
    }
  }

  /**
   * Get all active webhooks
   */
  static async getActiveWebhooks(): Promise<any[]> {
    return await CalendarWebhookModel.find({ isActive: true });
  }

  /**
   * Check and renew expiring webhooks
   */
  static async checkAndRenewExpiringWebhooks(): Promise<void> {
    try {
      const now = new Date();
      const renewalThreshold = new Date(now.getTime() + 2 * 60 * 60 * 1000); // 2 hours before expiration

      const expiringWebhooks = await CalendarWebhookModel.find({
        isActive: true,
        expiration: { $lte: renewalThreshold }
      });

      console.log(`Found ${expiringWebhooks.length} webhooks expiring within 2 hours`);

      let renewedCount = 0;
      let failedCount = 0;

      for (const webhook of expiringWebhooks) {
        console.log(`Renewing expiring webhook for therapist ${webhook.therapistId}, expires: ${webhook.expiration}`);

        const renewalSuccess = await this.renewWebhook(webhook.channelId);

        if (renewalSuccess) {
          renewedCount++;
        } else {
          failedCount++;
          console.error(`Failed to renew webhook for therapist ${webhook.therapistId}`);
        }
      }

      console.log(`Webhook renewal summary: ${renewedCount} renewed, ${failedCount} failed`);
    } catch (error: any) {
      console.error('Error in checkAndRenewExpiringWebhooks:', error.message);
    }
  }

  /**
   * Setup webhooks for all therapists
   */
  static async setupWebhooksForAllTherapists(): Promise<void> {
    try {
      // This would need to get all therapists from the database
      // const therapists = await TherapistDao.getAllTherapists();
      //
      // for (const therapist of therapists) {
      //   const googleCalendar = await GoogleCalendarService.findByTherapist(therapist._id);
      //   if (googleCalendar) {
      //     await this.setupWebhook(therapist._id);
      //   }
      // }

      console.log("Webhook setup completed for all therapists");
    } catch (error: any) {
      console.error("Error setting up webhooks for all therapists:", error.message);
    }
  }

  /**
   * Handle deleted events from Google Calendar
   */
  private static async handleDeletedEvents(therapistId: string, deletedEvents: any[]): Promise<void> {
    for (const deletedEvent of deletedEvents) {
      try {
        console.log(`Processing deleted event for therapist ${therapistId}:`, {
          eventId: deletedEvent.id,
          summary: deletedEvent.summary
        });

        await this.markEventAsDeleted(therapistId, deletedEvent);
      } catch (error: any) {
        console.error(`Error handling deleted event ${deletedEvent.id}:`, error.message);
      }
    }
  }

  /**
   * Mark event as deleted and cancel corresponding sessions
   */
  private static async markEventAsDeleted(therapistId: string, calendarEvent: any): Promise<void> {
    try {
      const ScheduleDao = require('../lib/dao/schedule.dao').ScheduleDao;
      const { ScheduleStatus } = require('../models/Schedule.model');

      // Find the schedule associated with this calendar event
      const schedule = await ScheduleDao.getScheduleById(calendarEvent.scheduleId);
      if (!schedule) {
        console.log(`No schedule found for deleted calendar event: ${calendarEvent.id}`);
        return;
      }

      // Find the specific recurrence date
      const recurrenceDate = schedule.recurrenceDates.find(
        (rd: any) => rd.calenderEventId && rd.calenderEventId.toString() === calendarEvent._id.toString()
      );

      if (recurrenceDate) {
        // Mark the specific session as cancelled
        recurrenceDate.status = ScheduleStatus.CANCELLED;
        // Add cancellation info to the other field since cancelledAt doesn't exist in interface
        (recurrenceDate as any).other = {
          ...((recurrenceDate as any).other || {}),
          isRefunded: (recurrenceDate as any).other?.isRefunded || false,
          isRescheduled: (recurrenceDate as any).other?.isRescheduled || false,
          cancelledAt: new Date(),
          cancelReason: 'Deleted from Google Calendar'
        };

        await schedule.save();
        console.log(`Marked session as cancelled for deleted event: ${calendarEvent.id}`);
      } else {
        // If it's a series event, mark all related sessions as cancelled
        const allRelatedSessions = schedule.recurrenceDates.filter(
          (rd: any) => rd.status !== ScheduleStatus.CANCELLED
        );

        for (const session of allRelatedSessions) {
          session.status = ScheduleStatus.CANCELLED;
          // Add cancellation info to the other field
          (session as any).other = {
            ...((session as any).other || {}),
            isRefunded: (session as any).other?.isRefunded || false,
            isRescheduled: (session as any).other?.isRescheduled || false,
            cancelledAt: new Date(),
            cancelReason: 'Series deleted from Google Calendar'
          };
        }

        await schedule.save();
        console.log(`Marked ${allRelatedSessions.length} sessions as cancelled for deleted series: ${calendarEvent.id}`);
      }

      // Update the calendar event status
      calendarEvent.status = 'cancelled';
      // Add deletedAt to the calendar event using type assertion
      (calendarEvent as any).deletedAt = new Date();
      await calendarEvent.save();

    } catch (error: any) {
      console.error(`Error marking event as deleted ${calendarEvent.id}:`, error.message);
    }
  }
}
