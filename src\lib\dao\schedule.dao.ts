import moment from "moment";
import { removeEmpty, removeEmpty<PERSON>eys } from "../../helper/custom.helper";
import ScheduleModel, { ScheduleStatus } from "../../models/Schedule.model";
import mongoose from "mongoose";

export class ScheduleDao {
  static async getAllByTherapist(
    therapistId: any,
    skip: any,
    pageSize: any,
    status: any
  ) {
    const query = removeEmpty({ therapistId: therapistId, status: status });
    return await ScheduleModel.find(query).skip(skip).limit(pageSize);
  }

  static async getByTherapistId(therapistId: any, skip?: any, pageSize?: any) {
    return await ScheduleModel.find(
      { therapistId: therapistId },
      "name email phone additionalEmails"
    )
      .skip(skip)
      .limit(pageSize);
  }

  // static async getAllRecurrenceDatesByTherapist(
  //   therapistId: any,
  //   status?: any,
  //   clientId?: any,
  //   searchText?: any
  // ) {
  //   let query = removeEmptyKeys({
  //     therapistId: String(therapistId),
  //     status: status,
  //     clientId: clientId,
  //   });
  //   console.log("query = ", query);
  //   if (searchText) {
  //     query["$or"] = [
  //       { name: { $regex: searchText, $options: "i" } },
  //       { email: { $regex: searchText, $options: "i" } },
  //     ];
  //   }
  //   return await ScheduleModel.find(
  //     query,
  //     "therapistId status recurrenceDates clientId name email additionalEmails"
  //   );
  // }

  static async getAllRecurrenceDatesByTherapist(
    therapistId: any,
    status?: any,
    clientId?: any,
    searchText?: any,
    startDate?: any,
    endDate?: any,
    // skip?: number,
    // limit?: number
    fromPublicCalender?: any
  ) {
    // const finalSkip = skip ?? 0; // Default to 0 if skip is undefined
    // const finalLimit = limit ?? 20;

    // Construct the base query with required filters
    let query: any = {
      therapistId: new mongoose.Types.ObjectId(therapistId),
      ...(clientId && {
        clientId: new mongoose.Types.ObjectId(clientId),
      }),
    };

    // If status is provided, filter on recurrenceDates array's status field
    if (status && status === "upcoming") {
      query["recurrenceDates.status"] = { $in: ["rescheduled", "confirmed"] };
    } else if (status) {
      query["recurrenceDates.status"] = status;
    }

    // Add search text filter if provided
    if (searchText) {
      query["$or"] = [
        { name: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
      ];
    }

    // Add public calender filter
    if (fromPublicCalender && fromPublicCalender === "true") {
      query["fromPublicCalender"] = true;
    }

    // Fixing date format if necessary
    const parsedStartDate = startDate
      ? new Date(startDate.replace(" 00:00", "+00:00"))
      : null;
    const parsedEndDate = endDate
      ? new Date(endDate.replace(" 00:00", "+00:00"))
      : null;

    // Date range filtering with validation
    if (startDate || endDate) {
      query["recurrenceDates.fromDate"] = {};
      if (startDate) {
        query["recurrenceDates.fromDate"]["$gte"] = parsedStartDate;
      }
      if (endDate) {
        query["recurrenceDates.fromDate"]["$lte"] = parsedEndDate;
      }
    }

    // Execute the query with pagination and necessary projections
    // return await ScheduleModel.find(query)
    //   //   .sort({ "recurrenceDates.fromDate": 1 })
    //   .skip(finalSkip)
    //   .limit(finalLimit)
    //   // .select("therapistId status recurrenceDates clientId name email additionalEmails")
    //   .populate("clientId", "name email isActive");

    return await ScheduleModel.aggregate([
      {
        $unwind: {
          path: "$recurrenceDates",
          // includeArrayIndex: 'string',
          // preserveNullAndEmptyArrays: true
        },
      },
      {
        $match: query,
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              "$$ROOT",
              {
                fromDate: "$recurrenceDates.fromDate",
                toDate: "$recurrenceDates.toDate",
                status: "$recurrenceDates.status",
                meetLink: "$recurrenceDates.meetLink",
                recurrenceDatesId: "$recurrenceDates._id",
                payTrackerId: "$recurrenceDates.payTrackerId",
                amount: "$recurrenceDates.amount",
              },
            ],
          },
        },
      },
      {
        $lookup: {
          from: "client",
          localField: "clientId",
          foreignField: "_id",
          as: "clientId",
          pipeline: [
            {
              $project: {
                _id: 1,
                name: 1,
                email: 1,
                isActive: 1,
                clientId: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: "$clientId",
      },
      {
        $sort: {
          fromDate: 1,
        },
      },
      // {
      //   $skip: finalSkip,
      // },
      // {
      //   $limit: finalLimit,
      // },
    ]);
  }

  // static async countSchedules(
  //   therapistId: any,
  //   status?: any,
  //   clientId?: any,
  //   searchText?: any,
  //   startDate?: any,
  //   endDate?: any
  // ) {
  //   // Construct the base query with required filters
  //   let query: any = {
  //     therapistId: String(therapistId),
  //     ...(clientId && { clientId: clientId }),
  //   };

  //   // If status is provided, filter on recurrenceDates array's status field
  //   if (status && status === "upcoming") {
  //     query["recurrenceDates.status"] = { $in: ["rescheduled", "confirmed"] };
  //   } else if (status) {
  //     query["recurrenceDates.status"] = status;
  //   }

  //   // Add search text filter if provided
  //   if (searchText) {
  //     query["$or"] = [
  //       { name: { $regex: searchText, $options: "i" } },
  //       { email: { $regex: searchText, $options: "i" } },
  //     ];
  //   }

  //   // Date range filtering
  //   if (startDate || endDate) {
  //     query["recurrenceDates.fromDate"] = {};
  //     if (startDate) {
  //       query["recurrenceDates.fromDate"]["$gte"] = startDate;
  //     }
  //     if (endDate) {
  //       query["recurrenceDates.fromDate"]["$lte"] = endDate;
  //     }
  //   }

  //   // Perform the count operation
  //   return await ScheduleModel.countDocuments(query);
  // }

  static async countSchedules(
    therapistId: any,
    status?: any,
    clientId?: any,
    searchText?: any,
    startDate?: any,
    endDate?: any
  ) {
    // Construct the base query with required filters
    let query: any = {
      therapistId: new mongoose.Types.ObjectId(therapistId),
      ...(clientId && {
        clientId: new mongoose.Types.ObjectId(clientId),
      }),
    };

    // If status is provided, filter on recurrenceDates array's status field
    if (status && status === "upcoming") {
      query["recurrenceDates.status"] = { $in: ["rescheduled", "confirmed"] };
    } else if (status) {
      query["recurrenceDates.status"] = status;
    }

    // Add search text filter if provided
    if (searchText) {
      query["$or"] = [
        { name: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
      ];
    }

    // Fixing date format if necessary
    const parsedStartDate = startDate
      ? new Date(startDate.replace(" 00:00", "+00:00"))
      : null;
    const parsedEndDate = endDate
      ? new Date(endDate.replace(" 00:00", "+00:00"))
      : null;

    // Date range filtering with validation
    if (startDate || endDate) {
      query["recurrenceDates.fromDate"] = {};
      if (startDate) {
        query["recurrenceDates.fromDate"]["$gte"] = parsedStartDate;
      }
      if (endDate) {
        query["recurrenceDates.fromDate"]["$lte"] = parsedEndDate;
      }
    }

    // Perform the aggregation with the final $count stage
    const result = await ScheduleModel.aggregate([
      {
        $unwind: {
          path: "$recurrenceDates",
          // includeArrayIndex: 'string',
          // preserveNullAndEmptyArrays: true
        },
      },
      {
        $match: query,
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              "$$ROOT",
              {
                fromDate: "$recurrenceDates.fromDate",
                toDate: "$recurrenceDates.toDate",
                status: "$recurrenceDates.status",
                meetLink: "$recurrenceDates.meetLink",
                recurrenceDatesId: "$recurrenceDates._id",
                payTrackerId: "$recurrenceDates.payTrackerId",
                amount: "$recurrenceDates.amount",
              },
            ],
          },
        },
      },
      {
        $lookup: {
          from: "client",
          localField: "clientId",
          foreignField: "_id",
          as: "clientId",
          pipeline: [
            {
              $project: {
                _id: 1,
                name: 1,
                email: 1,
                isActive: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: "$clientId",
      },
    ]);

    // Return the count from the result, or 0 if no documents matched
    return result.length;
  }

  static async getAllRecurrenceDates(therapistId?: any) {
    const query = removeEmpty({ therapistId: therapistId });
    return await ScheduleModel.find(
      query,
      "therapistId status recurrenceDates client"
    ).populate({
      path: "therapistId",
      select: "name email _id",
    });
  }

  static async getAllRecurrenceDatesForCron(therapistId: any) {
    const query = removeEmpty({ therapistId: therapistId });
    return await ScheduleModel.find(query).populate({
      path: "therapistId",
      select: "name email _id",
    });
  }

  static async createSchedule(scheduleData: any) {
    return await ScheduleModel.create(scheduleData);
  }

  static async getByRecurrenceDateId(recurrenceDateId: any) {
    return await ScheduleModel.findOne({
      "recurrenceDates._id": recurrenceDateId,
    }).populate({
      path: "clientId",
      select: "name email isActive",
    });
  }

  static async getScheduleByRecurrenceDateId(
    therapistId: any,
    recurrenceDateId: any
  ) {
    return await ScheduleModel.findOne({
      therapistId: therapistId,
      "recurrenceDates._id": recurrenceDateId,
    }).populate({ path: "clientId", select: "name email defaultTimezone" });
  }

  static async reschedule(
    therapistId: any,
    recurrenceDateId: any,
    fromDate: any,
    toDate: any,
    meetLink: any,
    calendarEventId: any
  ) {
    return await ScheduleModel.findOneAndUpdate(
      { therapistId: therapistId, "recurrenceDates._id": recurrenceDateId },
      {
        $set: {
          "recurrenceDates.$.fromDate": fromDate,
          "recurrenceDates.$.toDate": toDate,
          "recurrenceDates.$.status": ScheduleStatus.RESCHEDULED,
          "recurrenceDates.$.other.isReScheduled": true,
          "recurrenceDates.$.meetLink": meetLink,
          "recurrenceDates.$.calenderEventId": calendarEventId,
        },
      },
      { new: true }
    );
  }

  // static async allSchedule(
  //   therapistId: any,
  //   clientId: any,
  //   fromDate: any,
  //   toDate: any,
  //   meetLink: any,
  //   calendarEventId: any
  // ) {
  //   return await ScheduleModel.updateMany(
  //     { therapistId: therapistId, clientId: clientId },
  //     {
  //       $set: {
  //         "recurrenceDates.$[elem].fromDate": fromDate,
  //         "recurrenceDates.$[elem].toDate": toDate,
  //         "recurrenceDates.$[elem].status": ScheduleStatus.RESCHEDULED,
  //         "recurrenceDates.$[elem].other.isReScheduled": true,
  //         "recurrenceDates.$[elem].meetLink": meetLink,
  //         "recurrenceDates.$[elem].calenderEventId": calendarEventId,
  //       },
  //     },
  //     {
  //       arrayFilters: [
  //         { "elem.status": { $nin: ["completed", "cancelled"] } }, // Only update elements where status is not "completed"
  //       ],
  //       multi: true,
  //     }
  //   );
  // }

  static async allSchedule(
    therapistId: any,
    recurrenceDateId: any,
    clientId: any,
    newStartDate: any,
    toDate: any, // e.g., '2024-11-16T18:40:00.000Z'
    meetLink: any,
    calendarEventId: any
  ) {
    // Parse the new start date
    const startDate = new Date(newStartDate);
    const newDate = new Date(toDate);

    // Find the schedule with daily, weekly, biweekly, or no recurrence
    const schedule = await ScheduleModel.findOne({
      therapistId,
      clientId,
      "recurrenceDates._id": recurrenceDateId,
      $or: [
        { recurrence: "Does Not Repeat" },
        { recurrence: "Every Day" },
        { recurrence: "Every Week On Monday" },
        { recurrence: "Every Week On Tuesday" },
        { recurrence: "Every Week On Wednesday" },
        { recurrence: "Every Week On Thursday" },
        { recurrence: "Every Week On Friday" },
        { recurrence: "Every Week On Saturday" },
        { recurrence: "Every Week On Sunday" },
        { recurrence: "Every Two Weeks Monday" },
        { recurrence: "Every Two Weeks Tuesday" },
        { recurrence: "Every Two Weeks Wednesday" },
        { recurrence: "Every Two Weeks Thursday" },
        { recurrence: "Every Two Weeks Friday" },
        { recurrence: "Every Two Weeks Saturday" },
        { recurrence: "Every Two Weeks Sunday" },
      ],
    });

    if (!schedule) {
      throw new Error("Schedule not found");
    }

    // Check if the recurrence is "Does Not Repeat"
    if (schedule.recurrence === "Does Not Repeat") {
      // Update only the first eligible entry (the date that needs to be updated)
      const firstDateToUpdate: any = schedule.recurrenceDates.find(
        (item) => item.status !== "completed" && item.status !== "cancelled"
      );

      if (!firstDateToUpdate) {
        throw new Error("No date found to update.");
      }

      // Set the new start date as the fromDate, and keep the original toDate duration
      const originalDuration =
        firstDateToUpdate.toDate - firstDateToUpdate.fromDate; // Duration in milliseconds
      // const newToDate = new Date(startDate.getTime() + originalDuration); // Keep the original duration for toDate

      // Update the first date entry
      firstDateToUpdate.fromDate = startDate; // Update only the fromDate
      firstDateToUpdate.toDate = newDate; // Keep the original duration for toDate
      firstDateToUpdate.status = ScheduleStatus.RESCHEDULED;
      firstDateToUpdate.other = {
        ...firstDateToUpdate.other,
        isReScheduled: true, // Mark as rescheduled
      };
      firstDateToUpdate.meetLink = meetLink;
      firstDateToUpdate.calendarEventId = calendarEventId;

      // Save the updated schedule
      await schedule.save();
      return schedule;
    }

    // Find the index of the selected session in the recurrenceDates array
    const selectedSessionIndex = schedule.recurrenceDates.findIndex(
      (item) => item._id.toString() === recurrenceDateId.toString()
    );

    if (selectedSessionIndex === -1) {
      throw new Error("Selected session not found.");
    }

    // Filter dates that are eligible for update:
    // 1. Not completed or cancelled
    // 2. Must be the selected session or sessions after it (future sessions)
    const datesToUpdate = schedule.recurrenceDates.filter(
      (item, index) =>
        item.status !== "completed" &&
        item.status !== "cancelled" &&
        index >= selectedSessionIndex // Only include the selected session and future sessions
    );

    if (datesToUpdate.length === 0) {
      throw new Error("No dates found to update.");
    }

    // Determine the recurrence type: daily, weekly, or biweekly
    const isDaily = schedule.recurrence === "Every Day";
    const isBiweekly = schedule.recurrence.startsWith("Every Two Weeks");

    // Get all sessions in the schedule
    const allSessions = [...schedule.recurrenceDates];

    // Sort sessions by date to ensure proper order
    allSessions.sort((a, b) => moment(a.fromDate).diff(moment(b.fromDate)));

    // Create a map to store the updated sessions
    const updatedSessionsMap = new Map();

    // Generate new dates only for the selected session and future sessions
    const updatedDates = datesToUpdate.map((entry: any, index: any) => {
      const newFromDate = new Date(startDate);
      const newToDate = new Date(toDate);

      // Increment date by one day, one week, or two weeks based on recurrence pattern
      if (isDaily) {
        newFromDate.setDate(newFromDate.getDate() + index); // Daily increment
        newToDate.setDate(newDate.getDate() + index);
      } else if (isBiweekly) {
        newFromDate.setDate(newFromDate.getDate() + index * 14); // Biweekly increment
        newToDate.setDate(newDate.getDate() + index * 14);
      } else {
        newFromDate.setDate(newFromDate.getDate() + index * 7); // Weekly increment
        newToDate.setDate(newDate.getDate() + index * 7);
      }

      // Add this session to the map of updated sessions
      updatedSessionsMap.set(entry._id.toString(), true);

      return {
        ...entry.toObject(),
        fromDate: newFromDate,
        toDate: newToDate,
        status: ScheduleStatus.RESCHEDULED,
        other: {
          ...entry.other,
          isReScheduled: true, // Update the existing field inside the "other" object
        },
        meetLink,
        calendarEventId,
      };
    });

    // We need to be very careful to preserve the original sessions that are before the selected session

    // First, create a map of session IDs to their updated versions
    const updatedSessionsById = new Map();
    for (const updatedSession of updatedDates) {
      updatedSessionsById.set(updatedSession._id.toString(), updatedSession);
    }

    // Now, create a new array for the final recurrenceDates
    const finalRecurrenceDates = [];

    // Go through each session in the original schedule
    for (const session of schedule.recurrenceDates) {
      const sessionId = session._id.toString();

      // Check if this session is one that should be updated
      if (updatedSessionsById.has(sessionId)) {
        // This is a session that should be updated, add the updated version
        finalRecurrenceDates.push(updatedSessionsById.get(sessionId));
      } else {
        // This is a session that should not be updated (a past session), keep it as is
        finalRecurrenceDates.push(session);
      }
    }

    // Replace the schedule's recurrenceDates with the final array
    schedule.recurrenceDates = finalRecurrenceDates;

    // // Save the updated schedule
    await schedule.save();

    return schedule;
  }

  static async getByEmailForMultiple(therapistId: any, email: any) {
    return await ScheduleModel.findOne({
      therapistId: therapistId,
      $or: [
        { email: email },
        { additionalEmails: { $elemMatch: { $in: [email] } } },
      ],
      scheduleForMultiple: true,
    });
  }

  static async getByEmailForSingle(therapistId: any, email: any) {
    return await ScheduleModel.findOne({
      therapistId: therapistId,
      $or: [
        { email: email },
        { additionalEmails: { $elemMatch: { $in: [email] } } },
      ],
      scheduleForMultiple: { $ne: true },
    });
  }

  static async getByEmails(therapistId: any, emails: any) {
    return await ScheduleModel.findOne({
      therapistId: therapistId,
      $or: [{ email: { $in: emails } }, { additionalEmails: { $in: emails } }],
    });
  }

  static async updateByEmail(
    id: any,
    therapistId: any,
    email: any,
    requestedRecurrenceDates: any
  ) {
    return await ScheduleModel.findOneAndUpdate(
      { _id: id, therapistId: therapistId, email: email },
      {
        $push: {
          recurrenceDates: requestedRecurrenceDates,
        },
      },
      { new: true }
    );
  }

  static async updateForEmails(
    id: any,
    therapistId: any,
    requestedRecurrenceDates: any
  ) {
    return await ScheduleModel.findOneAndUpdate(
      { _id: id, therapistId: therapistId },
      {
        $push: {
          recurrenceDates: requestedRecurrenceDates,
        },
      },
      { new: true }
    );
  }

  static async cancelSchedule(therapistId: any, recurrenceDateId: any) {
    return await ScheduleModel.findOneAndUpdate(
      { therapistId: therapistId, "recurrenceDates._id": recurrenceDateId },
      {
        $set: {
          "recurrenceDates.$.status": ScheduleStatus.CANCELLED,
        },
      },
      { new: true }
    );
  }

  static async cancelAllSchedule(
    therapistId: any,
    clientId: any,
    recurrenceDateId: any
  ) {
    return await ScheduleModel.updateMany(
      {
        therapistId: therapistId,
        clientId: clientId,
        "recurrenceDates._id": recurrenceDateId,
      },
      {
        $set: {
          "recurrenceDates.$[elem].status": ScheduleStatus.CANCELLED,
        },
      },
      {
        arrayFilters: [
          {
            "elem.status": {
              $nin: [ScheduleStatus.COMPLETED, ScheduleStatus.CANCELLED],
            },
          }, // Only update elements where status is not "completed"
        ],
        multi: true,
      }
    );
  }

  static async markScheduleCompleted(therapistId: any, recurrenceDateId: any) {
    return await ScheduleModel.findOneAndUpdate(
      { therapistId: therapistId, "recurrenceDates._id": recurrenceDateId },
      {
        $set: {
          "recurrenceDates.$.status": ScheduleStatus.COMPLETED,
        },
      },
      { new: true }
    );
  }

  static async getById(therapistId: any, scheduleId: any) {
    return await ScheduleModel.findOne({
      therapistId: therapistId,
      _id: scheduleId,
    });
  }

  static async getScheduleById(scheduleId: any) {
    return await ScheduleModel.findById(scheduleId);
  }

  static async searchByName(therapistId: any, searchText: any) {
    return await ScheduleModel.find({
      therapistId: therapistId,
      name: { $regex: searchText, $options: "i" },
    });
  }

  static async searchByEmail(therapistId: any, searchText: any) {
    return await ScheduleModel.find({
      therapistId: therapistId,
      $or: [
        { email: { $regex: searchText, $options: "i" } },
        {
          additionalEmails: {
            $elemMatch: { $regex: searchText, $options: "i" },
          },
        },
      ],
    });
  }

  static async searchByEmailOrNameWithPagination(
    therapistId: any,
    searchText: any,
    skip?: any,
    pageSize?: any
  ) {
    return await ScheduleModel.find({
      therapistId: therapistId,
      $or: [
        { name: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
        {
          additionalEmails: {
            $elemMatch: { $regex: searchText, $options: "i" },
          },
        },
      ],
    })
      .skip(skip)
      .limit(pageSize);
  }

  static async searchByEmailOrName(therapistId: any, searchText: any) {
    return await ScheduleModel.find({
      therapistId: therapistId,
      $or: [
        { name: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
        {
          additionalEmails: {
            $elemMatch: { $regex: searchText, $options: "i" },
          },
        },
      ],
    });
  }

  static async getLastTwoWeekSessions(
    therapistId: any,
    beforeTwoWeek: any,
    startOfWeek: any
  ) {
    return await ScheduleModel.find({
      therapistId: therapistId,
      createdAt: { $gte: beforeTwoWeek, $lte: startOfWeek },
    });
  }

  static async updateRecurrenceDate(
    scheduleId: any,
    recurrenceDateId: any,
    transactionId: any,
    meetLink: string,
    calenderEventId: any,
    payTrackerId: any
  ) {
    return await ScheduleModel.findOneAndUpdate(
      { _id: scheduleId, "recurrenceDates._id": recurrenceDateId },
      {
        $set: {
          "recurrenceDates.$.transactionId": transactionId,
          "recurrenceDates.$.meetLink": meetLink,
          "recurrenceDates.$.calenderEventId": calenderEventId,
          "recurrenceDates.$.syncStatus": true,
          "recurrenceDates.$.cronStatus": true,
          "recurrenceDates.$.payTrackerId": payTrackerId,
        },
      },
      { new: true }
    );
  }

  static async updateScriptRecurrenceDate(
    scheduleId: any,
    recurrenceDateId: any,
    meetLink: string,
    calenderEventId: any
  ) {
    return await ScheduleModel.findOneAndUpdate(
      { _id: scheduleId, "recurrenceDates._id": recurrenceDateId },
      {
        $set: {
          "recurrenceDates.$.meetLink": meetLink,
          "recurrenceDates.$.calenderEventId": calenderEventId,
          "recurrenceDates.$.syncStatus": true,
          "recurrenceDates.$.cronStatus": true,
        },
      },
      { new: true }
    );
  }

  // static async updateRecurrenceDate(
  //   scheduleId: any,
  //   recurrenceDateId: any,
  //   transactionId: any,
  //   meetLink: string,
  //   calenderEventId: any,
  //   payTrackerId: any
  // ) {
  //   return await ScheduleModel.findOneAndUpdate(
  //     { _id: scheduleId, "recurrenceDates._id": recurrenceDateId },
  //     {
  //       $set: {
  //         "recurrenceDates.$[elem].transactionId": transactionId,
  //         "recurrenceDates.$[elem].meetLink": meetLink,
  //         "recurrenceDates.$[elem].calenderEventId": calenderEventId,
  //         "recurrenceDates.$[elem].syncStatus": true,
  //         "recurrenceDates.$[elem].cronStatus": true,
  //         "recurrenceDates.$[elem].payTrackerId": payTrackerId,
  //       },
  //     },
  //     {
  //       arrayFilters: [{ "elem._id": recurrenceDateId }],
  //       new: true,
  //     }
  //   );
  // }

  static async getScheduleByTherapistAndClientId(
    therapistId: any,
    clientId: any
  ) {
    return await ScheduleModel.findOne(
      { therapistId: therapistId, clientId: clientId },
      "recurrenceDates"
    ).populate({
      path: "recurrenceDates.transactionId",
      select: "paymentLink",
    });
  }

  static async getAllSchedulesOfClient(clientId: any) {
    return await ScheduleModel.find(
      { clientId: clientId },
      "recurrenceDates"
    ).populate({
      path: "recurrenceDates.transactionId",
      select: "paymentLink",
    });
  }
  static async updateRecurrenceStatusByTransactionId(
    transactionId: any,
    scheduleRecId: any
  ) {
    return await ScheduleModel.findOneAndUpdate(
      {
        "recurrenceDates.transactionId": transactionId,
        "recurrenceDates._id": scheduleRecId,
      },
      {
        $set: {
          "recurrenceDates.$.status": ScheduleStatus.CONFIRMED,
        },
      },
      { new: true }
    );
  }

  static async search(therapistId: any, searchText: any) {
    return await ScheduleModel.find({
      therapistId: therapistId,
      $or: [
        { name: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
      ],
    });
  }

  static async getScheduleByTherapistId(therapistId: any) {
    return await ScheduleModel.find({ therapistId: therapistId });
  }

  static async getScheduleByFilter(
    filter: any,
    projection: any = {},
    options: any = {}
  ) {
    const schedules = await ScheduleModel.aggregate([
      { $match: filter },
      { $sort: { createdAt: -1 } },
      {
        $addFields: {
          recurrenceDates: {
            $filter: {
              input: "$recurrenceDates",
              as: "record",
              cond: { $in: ["$$record.status", ["rescheduled", "confirmed"]] },
            },
          },
        },
      },
      {
        $addFields: {
          totalRecurrenceData: { $size: "$recurrenceDates" },
        },
      },
      { $match: { totalRecurrenceData: { $ne: 0 } } },
      {
        $addFields: {
          firstRecurrenceData: {
            $arrayElemAt: ["$recurrenceDates", 0],
          },
        },
      },
      { $project: { recurrenceDates: 0 } },
    ]);
    return schedules;
  }

  static async getCount(days: number) {
    const date = moment().subtract(days, "days").toDate();
    return await ScheduleModel.countDocuments({ createdAt: { $gte: date } });
  }

  static async getAll(
    pageSize: any,
    skip: any,
    therapistId: any,
    clientId: any
  ) {
    const query = removeEmpty({ therapistId: therapistId, clientId: clientId });
    return await ScheduleModel.find(query).skip(skip).limit(pageSize);
  }

  static async removeEventFromRecurrence(therapistId: any, recurranceId: any) {
    return await ScheduleModel.findOneAndUpdate(
      { therapistId: therapistId, "recurrenceDates._id": recurranceId },
      {
        $unset: {
          "recurrenceDates.$.calenderEventId": 1,
          "recurrenceDates.$.meetLink": 1,
        },
      },
      { new: true }
    );
  }

  static async markScheduleCompletedByRecId(recurrenceDateId: any) {
    return await ScheduleModel.findOneAndUpdate(
      { "recurrenceDates._id": recurrenceDateId },
      {
        $set: {
          "recurrenceDates.$.status": ScheduleStatus.COMPLETED,
        },
      },
      { new: true }
    );
  }

  static async cancelScheduleByCalendarEvent(
    therapistId: any,
    calendarEventId: any
  ) {
    return await ScheduleModel.findOneAndUpdate(
      {
        therapistId: therapistId,
        "recurrenceDates.calenderEventId": calendarEventId,
      },
      {
        $set: {
          "recurrenceDates.$.status": ScheduleStatus.CANCELLED,
        },
      },
      { new: true }
    );
  }

  static async getScheduleByScheduleIdTherapistAndClientId(
    _id: any,
    therapistId: any,
    clientId: any
  ) {
    return await ScheduleModel.findOne({
      _id: _id,
      therapistId: therapistId,
      clientId: clientId,
    });
  }

  static async updateRecurrenceAmount(
    therapistId: any,
    scheduleId: any,
    recurranceId: any,
    amount: any,
    transactionId: any
  ) {
    return await ScheduleModel.findOneAndUpdate(
      {
        _id: scheduleId,
        therapistId: therapistId,
        "recurrenceDates._id": recurranceId,
      },
      {
        $set: {
          "recurrenceDates.$.amount": amount,
          "recurrenceDates.$.transactionId": transactionId,
        },
      },
      { new: true }
    );
  }

  static async updateRecurrenceAmountForPayTracker(
    therapistId: any,
    scheduleId: any,
    recurranceId: any,
    amount: any,
    trackingId: any
  ) {
    return await ScheduleModel.findOneAndUpdate(
      {
        _id: scheduleId,
        therapistId: therapistId,
        "recurrenceDates._id": recurranceId,
      },
      {
        $set: {
          "recurrenceDates.$.amount": amount,
          "recurrenceDates.$.payTrackerId": trackingId,
        },
      },
      { new: true }
    );
  }

  static async updateRecurrenceAmountZero(
    therapistId: any,
    scheduleId: any,
    recurranceId: any,
    amount: any
  ) {
    return await ScheduleModel.findOneAndUpdate(
      {
        _id: scheduleId,
        therapistId: therapistId,
        "recurrenceDates._id": recurranceId,
      },
      {
        $set: {
          "recurrenceDates.$.amount": amount,
          "recurrenceDates.$.status": ScheduleStatus.CONFIRMED,
        },
      },
      { new: true }
    );
  }

  static async getScheduleClientData(therapistId: any, recurranceId: any) {
    return await ScheduleModel.findOne({
      therapistId: therapistId,
      "recurrenceDates._id": recurranceId,
    }).populate("clientId");
  }

  static async getSchedulesByTherapistAndClientId(
    therapistId: any,
    clientId: any
  ) {
    return await ScheduleModel.find({
      therapistId: therapistId,
      clientId: clientId,
    });
  }

  static async cancelScheduleWithPendingStatus(
    therapistId: any,
    recurrenceDateId: any
  ) {
    const filter = {
      therapistId: therapistId,
      "recurrenceDates._id": recurrenceDateId,
      "recurrenceDates.status": ScheduleStatus.PENDING,
    };

    const update = {
      $set: {
        "recurrenceDates.$[elem].status": ScheduleStatus.CANCELLED, // Use the arrayFilters identifier
        updatedAt: new Date(), // Use a dynamic value for updatedAt
      },
      $setOnInsert: {
        createdAt: new Date(), // This only applies if a new document is inserted
      },
    };

    const options = {
      new: true,
      arrayFilters: [{ "elem._id": recurrenceDateId }], // Define the array filter
      upsert: false, // Set to true if you want to insert a new doc if no doc matches the filter
    };

    const updatedSchedule = await ScheduleModel.findOneAndUpdate(
      filter,
      update,
      options
    );

    return updatedSchedule;
  }

  static async getScheduleWithPendingStatus(
    therapistId: any,
    recurrenceDateId: any
  ) {
    const filter = {
      therapistId: therapistId,
      "recurrenceDates._id": recurrenceDateId,
      "recurrenceDates.status": ScheduleStatus.PENDING,
    };

    const getSchedule = await ScheduleModel.findOne(filter);

    return getSchedule;
  }

  static async getSchedulesRecurrenceId(
    therapistId: any,
    recurrenceDateId: any
  ) {
    const filter = {
      therapistId: therapistId,
      "recurrenceDates._id": recurrenceDateId,
    };

    const getSchedule = await ScheduleModel.findOne(filter);

    return getSchedule;
  }

  static async deleteRecurrance(recc_id: any) {
    return await ScheduleModel.updateMany(
      { "recurrenceDates._id": recc_id },
      { $pull: { recurrenceDates: { _id: recc_id } } }
    );
  }

  static async getTherapistSchedulesInDateRange(
    therapistId: any,
    fromDate: any,
    toDate: any
  ) {
    return await ScheduleModel.find({
      therapistId: therapistId,
      "recurrenceDates.fromDate": { $gte: fromDate, $lte: toDate },
    });
  }

  static async getTherapistClientByScheduleId(id: any) {
    return await ScheduleModel.findOne({ _id: id }, "name")
      .populate({
        path: "clientId",
        select: "name",
      })
      .populate({
        path: "therapistId",
        select: "name",
      });
  }

  /**
   * Find schedules by calendar series ID (iCalUID)
   */
  static async findByCalendarSeriesId(calendarSeriesId: any) {
    // Find calendar events with the same iCalUID (series)
    const CalendarEventModel = require("../../models/calendarEvent.model").default;
    const calendarEvents = await CalendarEventModel.find({
      iCalUID: calendarSeriesId
    });

    if (!calendarEvents || calendarEvents.length === 0) {
      return [];
    }

    // Get all schedule IDs from the calendar events
    const scheduleIds = calendarEvents.map((event: any) => event.scheduleId);

    // Find all schedules that contain these calendar events
    return await ScheduleModel.find({
      _id: { $in: scheduleIds }
    });
  }

  static async getAllSchedulesInDateRange(fromDate: any) {
    return await ScheduleModel.find({
      "recurrenceDates.fromDate": { $gte: fromDate },
    }).populate({
      path: "therapistId",
      select: "menus",
    });
  }

  static async getLatestScheduleByTherapistAndClientId(
    therapistId: any,
    clientId: any
  ) {
    return await ScheduleModel.findOne({
      clientId: clientId,
      therapistId: therapistId,
    }).sort({ createdAt: -1 });
  }
}
