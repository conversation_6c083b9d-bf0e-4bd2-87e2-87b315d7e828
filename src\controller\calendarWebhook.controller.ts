/**
 * Calendar Webhook Controller
 *
 * This controller handles Google Calendar webhook endpoints:
 * - Receives webhook notifications from Google Calendar
 * - Manages webhook setup and lifecycle
 * - Provides webhook status and management endpoints
 * - Handles calendar series rescheduling triggered by webhooks
 */

import express from "express";
import { CalendarWebhookService } from "../services/calendarWebhook.service";
import { OptimizedCalendarSyncService } from "../services/optimizedCalendarSync.service";
import { Response } from "../util/response";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { ScheduleStatus } from "../models/Schedule.model";

export class CalendarWebhookController {

  /**
   * Handle incoming Google Calendar webhook notifications
   */
  static async handleWebhookNotification(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      // Enhanced logging for webhook debugging
      console.log(`🔔 Webhook notification received at ${new Date().toISOString()}`);
      console.log(`📋 Headers:`, {
        'x-goog-channel-id': req.headers['x-goog-channel-id'],
        'x-goog-resource-state': req.headers['x-goog-resource-state'],
        'x-goog-resource-id': req.headers['x-goog-resource-id'],
        'x-goog-resource-uri': req.headers['x-goog-resource-uri'],
        'x-goog-channel-token': req.headers['x-goog-channel-token'],
        'x-goog-event-type': req.headers['x-goog-event-type'],
        'x-goog-event-id': req.headers['x-goog-event-id'],
        'content-type': req.headers['content-type'],
        'user-agent': req.headers['user-agent']
      });

      if (req.body && Object.keys(req.body).length > 0) {
        console.log(`📄 Body:`, req.body);
      }

      // Google Calendar sends notifications via POST with specific headers
      const result = await CalendarWebhookService.processWebhookNotification(
        req.headers,
        req.body
      );

      if (result.success) {
        console.log(`✅ Webhook notification processed successfully`);
        // Respond with 200 to acknowledge receipt
        res.status(200).send("OK");
      } else {
        console.error(`❌ Webhook processing failed:`, result.message);
        res.status(400).send(result.message);
      }
    } catch (error: any) {
      console.error(`💥 Error in webhook handler:`, error.message);
      console.error(`Stack trace:`, error.stack);
      res.status(500).send("Internal server error");
    }
  }

  /**
   * Setup webhook for a therapist
   */
  static async setupWebhook(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      const channel = await CalendarWebhookService.setupWebhook(therapistId);

      if (channel) {
        res.status(200).send(
          new Response(
            {
              channelId: channel.id,
              expiration: channel.expiration,
              isActive: channel.isActive
            },
            "Webhook setup successful",
            200
          )
        );
      } else {
        res.status(400).send(
          new Response(
            null,
            "Failed to setup webhook",
            400
          )
        );
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Stop webhook for a therapist
   */
  static async stopWebhook(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      // Find active webhook for this therapist
      const CalendarWebhookModel = require("../models/CalendarWebhook.model").default;
      const activeWebhook = await CalendarWebhookModel.findOne({
        therapistId: therapistId,
        isActive: true
      });

      if (!activeWebhook) {
        return res.status(404).send(
          new Response(
            null,
            "No active webhook found",
            404
          )
        );
      }

      // Stop the webhook using the found channelId
      const success = await CalendarWebhookService.stopWebhook(activeWebhook.channelId);

      if (success) {
        res.status(200).send(
          new Response(
            {
              stopped: true,
              channelId: activeWebhook.channelId
            },
            "Webhook stopped successfully",
            200
          )
        );
      } else {
        res.status(400).send(
          new Response(
            null,
            "Failed to stop webhook",
            400
          )
        );
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get webhook status for a therapist
   */
  static async getWebhookStatus(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const CalendarWebhookModel = require("../models/CalendarWebhook.model").default;

      const therapistWebhooks = await CalendarWebhookModel.find({
        therapistId: therapistId,
        isActive: true
      });

      res.status(200).send(
        new Response(
          {
            webhooks: therapistWebhooks,
            count: therapistWebhooks.length,
            hasActiveWebhook: therapistWebhooks.length > 0
          },
          "Webhook status retrieved successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Trigger manual sync for a therapist
   */
  static async triggerManualSync(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const { incremental = true, maxResults = 100 } = req.body;

      const syncResult = await OptimizedCalendarSyncService.performIncrementalSync(
        therapistId,
        {
          incremental,
          maxResults
        }
      );

      res.status(200).send(
        new Response(
          {
            success: syncResult.success,
            syncedEvents: syncResult.syncedEvents,
            updatedEvents: syncResult.updatedEvents,
            createdEvents: syncResult.createdEvents,
            errors: syncResult.errors,
            conflicts: syncResult.conflicts
          },
          syncResult.success ? "Manual sync completed successfully" : "Manual sync completed with errors",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Start automatic sync for all therapists
   */
  static async startAutomaticSync(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const { intervalMinutes = 3 } = req.body;

      const { AutomaticCalendarSyncService } = require('../services/automaticCalendarSync.service');
      AutomaticCalendarSyncService.startAutomaticSync(intervalMinutes);

      res.status(200).send(
        new Response(
          {
            started: true,
            intervalMinutes,
            message: `Automatic sync started - will run every ${intervalMinutes} minutes`
          },
          "Automatic sync started successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Stop automatic sync
   */
  static async stopAutomaticSync(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const { AutomaticCalendarSyncService } = require('../services/automaticCalendarSync.service');
      AutomaticCalendarSyncService.stopAutomaticSync();

      res.status(200).send(
        new Response(
          { stopped: true },
          "Automatic sync stopped successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get automatic sync status
   */
  static async getAutomaticSyncStatus(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const { AutomaticCalendarSyncService } = require('../services/automaticCalendarSync.service');
      const status = AutomaticCalendarSyncService.getSyncStatus();

      res.status(200).send(
        new Response(
          status,
          "Automatic sync status retrieved successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Trigger immediate sync for all therapists
   */
  static async triggerImmediateSync(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const { AutomaticCalendarSyncService } = require('../services/automaticCalendarSync.service');
      const result = await AutomaticCalendarSyncService.triggerImmediateSync();

      res.status(200).send(
        new Response(
          result,
          result.success ? "Immediate sync completed successfully" : "Immediate sync completed with errors",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }





  /**
   * Renew webhook for a therapist
   */
  static async renewWebhook(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      // Find active webhook for this therapist
      const CalendarWebhookModel = require("../models/CalendarWebhook.model").default;
      const activeWebhook = await CalendarWebhookModel.findOne({
        therapistId: therapistId,
        isActive: true
      });

      if (!activeWebhook) {
        return res.status(404).send(
          new Response(
            null,
            "No active webhook found to renew",
            404
          )
        );
      }

      // Stop the old webhook
      await CalendarWebhookService.stopWebhook(activeWebhook.channelId);

      // Create a new webhook
      const newChannel = await CalendarWebhookService.setupWebhook(therapistId);

      if (newChannel) {
        res.status(200).send(
          new Response(
            {
              renewed: true,
              oldChannelId: activeWebhook.channelId,
              newChannelId: newChannel.id,
              expiration: newChannel.expiration,
              isActive: newChannel.isActive
            },
            "Webhook renewed successfully",
            200
          )
        );
      } else {
        res.status(400).send(
          new Response(
            null,
            "Failed to renew webhook",
            400
          )
        );
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get sync statistics for a therapist
   */
  static async getSyncStatistics(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const CalendarWebhookModel = require("../models/CalendarWebhook.model").default;

      const webhook = await CalendarWebhookModel.findOne({
        therapistId: therapistId,
        isActive: true
      });

      if (!webhook) {
        return res.status(404).send(
          new Response(
            null,
            "No active webhook found",
            404
          )
        );
      }

      res.status(200).send(
        new Response(
          {
            totalSyncs: webhook.syncStatistics.totalSyncs,
            successfulSyncs: webhook.syncStatistics.successfulSyncs,
            failedSyncs: webhook.syncStatistics.failedSyncs,
            lastSyncAt: webhook.syncStatistics.lastSyncAt,
            eventsProcessed: webhook.syncStatistics.eventsProcessed,
            conflictsDetected: webhook.syncStatistics.conflictsDetected,
            conflictsResolved: webhook.syncStatistics.conflictsResolved,
            notificationCount: webhook.notificationCount,
            lastNotificationAt: webhook.lastNotificationAt
          },
          "Sync statistics retrieved successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Test webhook connectivity and debugging
   */
  static async testWebhookConnectivity(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const CalendarWebhookModel = require("../models/CalendarWebhook.model").default;

      // Get webhook status
      const activeWebhooks = await CalendarWebhookModel.find({
        therapistId,
        isActive: true
      }).sort({ createdAt: -1 });

      const testResults = {
        webhookStatus: {
          hasActiveWebhook: activeWebhooks.length > 0,
          activeWebhooks: activeWebhooks.map((w: any) => ({
            channelId: w.channelId,
            expiration: w.expiration,
            isExpired: w.isExpired(),
            isExpiringSoon: w.isExpiringSoon(),
            lastNotificationAt: w.lastNotificationAt,
            notificationCount: w.notificationCount,
            syncStatistics: w.syncStatistics
          }))
        },
        connectivity: {
          webhookUrl: `${process.env.WEBHOOK_BASE_URL || process.env.FRONTEND_BASEURL}/api/v1/webhook/calendar`,
          timestamp: new Date().toISOString(),
          environment: process.env.NODE_ENV || 'development'
        },
        troubleshooting: {
          commonIssues: [
            "Webhook URL not accessible from Google servers",
            "Webhook expired and needs renewal",
            "Google Calendar not sending notifications for series changes",
            "Firewall blocking incoming webhook requests",
            "ngrok tunnel not running (in development)"
          ],
          nextSteps: [
            "1. Check if webhook URL is publicly accessible",
            "2. Verify webhook hasn't expired",
            "3. Test by making a small change in Google Calendar",
            "4. Check server logs for incoming webhook notifications",
            "5. Ensure ngrok is running and URL is correct (development)"
          ],
          debuggingTips: [
            "Monitor server logs when making calendar changes",
            "Check webhook notification count increases after changes",
            "Verify webhook expiration date",
            "Test webhook URL accessibility from external tools"
          ]
        }
      };

      res.status(200).send(
        new Response(
          testResults,
          "Webhook connectivity test completed",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all active webhooks (admin endpoint)
   */
  static async getAllActiveWebhooks(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const activeWebhooks = await CalendarWebhookService.getActiveWebhooks();

      res.status(200).send(
        new Response(
          {
            webhooks: activeWebhooks,
            count: activeWebhooks.length
          },
          "Active webhooks retrieved successfully",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Setup webhooks for all therapists (admin endpoint)
   */
  static async setupAllWebhooks(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      await CalendarWebhookService.setupWebhooksForAllTherapists();

      res.status(200).send(
        new Response(
          { initiated: true },
          "Webhook setup initiated for all therapists",
          200
        )
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reschedule calendar series - handles webhook-triggered series rescheduling
   */
  static async rescheduleCalendarSeries(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const {
        calendarSeriesId,
        calendarEventId,
        newStartTime,
        newEndTime,
        rescheduleType = 'entire_series' // 'single_session', 'partial_series', 'entire_series'
      } = req.body;

      if (!calendarSeriesId || !newStartTime || !newEndTime) {
        return res.status(400).json({
          success: false,
          message: "Calendar series ID, new start time, and new end time are required",
        });
      }

      // Find all sessions in the series
      const sessions = await ScheduleDao.findByCalendarSeriesId(calendarSeriesId);
      if (!sessions || sessions.length === 0) {
        return res.status(404).json({
          success: false,
          message: "No sessions found for the specified calendar series",
        });
      }

      let updatedSessions = 0;
      let errors = [];

      // Handle different reschedule types
      switch (rescheduleType) {
        case 'single_session':
          // Reschedule only the specific session
          const singleSession = sessions.find(s =>
            s.recurrenceDates.some(rd => rd.calenderEventId?.toString() === calendarEventId)
          );
          if (singleSession) {
            const recDate = singleSession.recurrenceDates.find(rd =>
              rd.calenderEventId?.toString() === calendarEventId
            );
            if (recDate) {
              recDate.fromDate = new Date(newStartTime);
              recDate.toDate = new Date(newEndTime);
              recDate.status = ScheduleStatus.RESCHEDULED;
              await singleSession.save();
              updatedSessions = 1;
            }
          }
          break;

        case 'entire_series':
          // Reschedule sessions from the specified event onwards
          for (const session of sessions) {
            try {
              // Find the specific recurrence date that matches the calendarEventId
              const targetRecurrence = session.recurrenceDates.find(rd =>
                rd.calenderEventId?.toString() === calendarEventId
              );

              if (!targetRecurrence) {
                console.warn(`Target recurrence not found for calendarEventId: ${calendarEventId}`);
                continue;
              }

              // Calculate time difference based on the target recurrence, not the first one
              const timeDiff = new Date(newStartTime).getTime() - targetRecurrence.fromDate.getTime();

              // Sort recurrence dates by date to ensure proper ordering
              const sortedRecurrences = [...session.recurrenceDates].sort((a, b) =>
                new Date(a.fromDate).getTime() - new Date(b.fromDate).getTime()
              );

              // Find the index of the target recurrence in the sorted array
              const targetIndex = sortedRecurrences.findIndex(rd =>
                rd._id.toString() === targetRecurrence._id.toString()
              );

              if (targetIndex === -1) {
                console.warn(`Target recurrence index not found for: ${targetRecurrence._id}`);
                continue;
              }

              // Only reschedule sessions from the target session onwards
              for (let i = targetIndex; i < sortedRecurrences.length; i++) {
                const recDate = sortedRecurrences[i];
                const newFromDate = new Date(recDate.fromDate.getTime() + timeDiff);
                const newToDate = new Date(recDate.toDate.getTime() + timeDiff);

                // Ensure the new date is not in the past
                if (newFromDate.getTime() >= Date.now()) {
                  recDate.fromDate = newFromDate;
                  recDate.toDate = newToDate;
                  recDate.status = ScheduleStatus.RESCHEDULED;

                  console.log(`Rescheduled session ${recDate._id} from ${recDate.fromDate} to ${newFromDate}`);
                } else {
                  console.warn(`Skipping past date for session ${recDate._id}: ${newFromDate}`);
                }
              }

              await session.save();
              updatedSessions++;
            } catch (error: any) {
              console.error(`Error rescheduling session ${session._id}:`, error.message);
              errors.push({
                sessionId: session._id,
                error: error.message
              });
            }
          }
          break;

        case 'partial_series':
          // Reschedule sessions from the specified event onwards (same logic as entire_series)
          for (const session of sessions) {
            try {
              // Find the specific recurrence date that matches the calendarEventId
              const targetRecurrence = session.recurrenceDates.find(rd =>
                rd.calenderEventId?.toString() === calendarEventId
              );

              if (!targetRecurrence) {
                console.warn(`Target recurrence not found for calendarEventId: ${calendarEventId}`);
                continue;
              }

              // Calculate time difference based on the target recurrence
              const timeDiff = new Date(newStartTime).getTime() - targetRecurrence.fromDate.getTime();

              // Sort recurrence dates by date to ensure proper ordering
              const sortedRecurrences = [...session.recurrenceDates].sort((a, b) =>
                new Date(a.fromDate).getTime() - new Date(b.fromDate).getTime()
              );

              // Find the index of the target recurrence in the sorted array
              const targetIndex = sortedRecurrences.findIndex(rd =>
                rd._id.toString() === targetRecurrence._id.toString()
              );

              if (targetIndex === -1) {
                console.warn(`Target recurrence index not found for: ${targetRecurrence._id}`);
                continue;
              }

              // Only reschedule sessions from the target session onwards
              for (let i = targetIndex; i < sortedRecurrences.length; i++) {
                const recDate = sortedRecurrences[i];
                const newFromDate = new Date(recDate.fromDate.getTime() + timeDiff);
                const newToDate = new Date(recDate.toDate.getTime() + timeDiff);

                // Ensure the new date is not in the past
                if (newFromDate.getTime() >= Date.now()) {
                  recDate.fromDate = newFromDate;
                  recDate.toDate = newToDate;
                  recDate.status = ScheduleStatus.RESCHEDULED;

                  console.log(`Rescheduled session ${recDate._id} from ${recDate.fromDate} to ${newFromDate}`);
                } else {
                  console.warn(`Skipping past date for session ${recDate._id}: ${newFromDate}`);
                }
              }

              await session.save();
              updatedSessions++;
            } catch (error: any) {
              console.error(`Error rescheduling session ${session._id}:`, error.message);
              errors.push({
                sessionId: session._id,
                error: error.message
              });
            }
          }
          break;

        default:
          return res.status(400).json({
            success: false,
            message: "Invalid reschedule type",
          });
      }

      res.status(200).json({
        success: true,
        message: `Successfully rescheduled ${updatedSessions} session(s)`,
        data: {
          updatedSessions,
          rescheduleType,
          errors: errors.length > 0 ? errors : undefined
        }
      });

    } catch (error) {
      console.error("Error in rescheduleCalendarSeries:", error);
      next(error);
    }
  }
}
