{"name": "therapist-back-office-api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"server": "tsc && node ./build/index.js", "build": "tsc", "start": "tsc && node ./build/index.js", "ts:watch": "tsc -w", "test": "jest --watchAll --verbose --runInBand --coverage", "dev": "nodemon --exec ts-node --files ./src/index.ts", "createAdmin": "ts-node ./src/scripts/createAdmin.ts", "menuCreate": "ts-node ./src/scripts/menuCreate.ts", "createIdentifier": "ts-node ./src/scripts/createIdentifier.ts", "deleteDuplicateSchedules": "ts-node ./src/scripts/deleteDuplicateSchedules", "updateSchedulePaytracker": "ts-node ./src/scripts/paytrackerSchedules.ts", "paytrackerpopulate": "ts-node ./src/scripts/paytrackerpopulate.ts", "debug-webhook": "ts-node ./src/scripts/debugWebhookNotifications.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@googleapis/docs": "^2.0.1", "@sendgrid/mail": "^7.7.0", "@types/bcrypt": "^5.0.0", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.9", "@types/node": "^20.4.6", "@types/node-cron": "^3.0.8", "@types/uuid": "^9.0.2", "api": "^6.1.0", "aws-sdk": "^2.1432.0", "axios": "^1.4.0", "bcrypt": "^5.1.1", "cashfree-sdk": "^0.1.1", "cors": "^2.8.5", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.21.1", "googleapis": "^123.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.1", "moment": "^2.29.4", "moment-timezone": "^0.5.44", "mongoose": "^7.4.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "nodemon": "^3.1.4", "razorpay": "^2.9.2", "stripe": "^14.1.0", "ts-node": "^10.9.1", "typescript": "^5.1.6", "uuid": "^9.0.0"}}