import express from "express";
import { CONFIG } from "../config/environment";
import { google } from "googleapis";
import { TherapistService } from "../services/therapist.service";
import { Utility } from "../util/util";
import axios from "axios";
import { GoogleRequestService } from "../services/googleReq.service";
import { v4 as uuidv4 } from "uuid";
import { KeysDao } from "../lib/dao/keys.dao";
import { SymmetricCryptService } from "../services/symmetricCrypt";
import {
  GoogleCalendarService,
  IAddEventPayload,
} from "../services/googleCalendar.service";
import { Response, throwError } from "../util/response";
import { testingTemplate } from "../util/emailTemplate/testingTemplate";
import { Mailer2 } from "../util/mailer2";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import moment from "moment";
import {
  combineDateWithTime,
  createDailyRecurrences,
  createRRule,
  doIntervalsOverlap,
  findOverlapDate,
  isOverlap,
  mergeDate,
} from "../helper/custom.helper";
import {
  ICreateScheduleData,
  ScheduleService,
} from "../services/schedule.service";
import { FileUploadService } from "../services/uploadService";
import fs from "fs";
import { TransactionService } from "../services/transaction.service";
import { ClientService } from "../services/client.service";
// import { mailWithoutPayLinkToClient, scheduleEmailSentToClient, scheduleEmailSentToOfflineClient, scheduleEmailWhenIsBeforeFalse } from "../util/emailTemplate/scheduleEmailSentToClient";
import { paymentStatus } from "../lib/enum/cashfree.enum";
import { ClientDao } from "../lib/dao/client.dao";
import { TransactionDao } from "../lib/dao/transaction.dao";
import { FileDownloadService } from "../services/downloadService";
import { TherapistDao } from "../lib/dao/therapist.dao";
import ScheduleModel, { ScheduleStatus } from "../models/Schedule.model";
import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
import LogsModel from "../models/Logs.model";
import { reScheduleEmail } from "../util/emailTemplate/rescheduleEmail";
import { cancelScheduleEmail } from "../util/emailTemplate/cancelScheduleEmail";
import { GoogleCalendarDao } from "../lib/dao/googleCalendar.dao";
import calendarEventModel from "../models/calendarEvent.model";
import ClientModel, { GenderEnum } from "../models/Client.model";
import RazorpayService from "../services/razorpay.service";
import { sendOtpForPassword } from "../util/emailTemplate/sendOtp";
import InvoiceService from "../services/invoice.service";
import { DeductionService } from "../services/deductionService";
import deductionModel, { DeductionTypeEnum } from "../models/deduction.model";
import createScheduleTemplate from "../util/emailTemplate/create.schedule";
import { MailSubjectEnum } from "../lib/enum/subject.enum";
import momentTimezone from "moment-timezone";
import { PayTrackerService } from "../services/payTracker.service";
import PayTrackerModel, {
  CurrencyEnum,
  PaymentTrackerStatusEnum,
  PaymentTrackerTypeEnum,
} from "../models/PayTracker.model";
import TherapistSubscriptionService from "../services/therapistsubscription.service";
import { PayoutService } from "../services/payout.service";
import { PayoutStatus } from "../models/payouts.model";
import ExcelJS from "exceljs";
import path from "path";
import { string } from "joi";
import mongoose from "mongoose";
import scheduleReminder from "../util/emailTemplate/scheduleReminder";
import { rescheduleSlot } from "../util/emailTemplate/rescheduleSlot";
import { BrevoWhatsapp } from "../util/brevoWhatsapp";
import { sendWhatsAppNotification } from "../services/whatsAppNotification.service";
import { format } from "date-fns";
import { RescheduleConflictService } from "../services/rescheduleConflict.service";
import { getErrorResponse } from "../helper/error.handler";

interface CalendarEvent {
  scheduleId?: any;
  scheduleRecId?: any;
  transactionId?: any;
  meetLink?: string;
  calenderEventId?: any;
  payTrackerId?: any;
  _id?: any;
  link?: any;
  start?: {
    dateTime?: any;
  };
}

interface GoogleCalendarEvent {
  link: string;
  calenderEventId: CalendarEvent[];
}
const scopes = [
  "https://www.googleapis.com/auth/calendar",
  "https://www.googleapis.com/auth/calendar.readonly",
  "https://www.googleapis.com/auth/userinfo.profile",
  "https://www.googleapis.com/auth/userinfo.email",
];
export class TherapistController {
  static async signup(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );

      const data = await oauth2Client.generateAuthUrl({
        access_type: CONFIG.accessType,
        prompt: "consent",
        scope: scopes,
      });
      res.send(data);
    } catch (error) {
      next(error);
    }
  }

  static async createTherapist(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );
      const code: any = req.query.code;
      const { tokens } = await oauth2Client.getToken(code);
      // Get the user's granted scopes

      if (!tokens.access_token) {
        return res.status(400).json({ error: "Access token is missing." });
      }
      const tokenInfo = await oauth2Client.getTokenInfo(tokens.access_token);
      const grantedScopes = tokenInfo.scopes || [];

      // Check if all required scopes are granted
      const missingScopes = scopes.filter(
        (scope) => !grantedScopes.includes(scope)
      );

      if (missingScopes.length > 0) {
        return res.status(403).json({
          error: "You must grant all required permissions.",
          missingScopes,
        });
      }
      const userData = await GoogleRequestService.makeRequest(
        "/oauth2/v2/userinfo",
        "GET",
        tokens.access_token
      );

      const allCalendarData = await GoogleRequestService.makeRequest(
        "/calendar/v3/users/me/calendarList",
        "GET",
        tokens.access_token
      );

      for (let calendar of allCalendarData.items) {
        const calendarById = await GoogleRequestService.makeRequest(
          "/calendar/v3/calendars/" + calendar.id,
          "GET",
          tokens.access_token
        );
      }

      const keyToEncryptDataOfTherapist =
        await SymmetricCryptService.createRandomKey();
      let existingTherapist = await TherapistService.findByEmail(
        userData.email
      );
      if (!existingTherapist) {
        existingTherapist = await TherapistService.createTherapist({
          email: userData.email,
          name: userData.name,
        });
        if (!existingTherapist) {
          return res
            .status(400)
            .json({ success: false, message: "Unable to create therapist." });
        }

        const existingKeyForTherapist = await KeysDao.findByTherapist(
          existingTherapist._id
        );
        if (!existingKeyForTherapist) {
          await KeysDao.createKey(
            keyToEncryptDataOfTherapist,
            existingTherapist._id
          );
        }
      }

      const existingGoogleCalendar =
        await GoogleCalendarService.findByTherapist(existingTherapist._id);
      if (!existingGoogleCalendar) {
        const googleCalendar = await GoogleCalendarService.create({
          access_token: tokens.access_token,
          refresh_token: tokens.refresh_token,
          access_code: req.query.code,
          therapist: existingTherapist._id,
        });
        if (!googleCalendar) {
          return res.status(400).json({
            success: false,
            message: "Unable to create google calendar creds.",
          });
        }
      } else {
        await GoogleCalendarService.update(existingGoogleCalendar._id, {
          access_token: tokens.access_token,
          refresh_token: tokens.refresh_token,
          access_code: req.query.code,
          therapist: existingTherapist._id,
        });
      }

      res.send("loggedin");
    } catch (error) {
      next(error);
    }
  }

  static async login(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const email = req.body.email;
      const user = await TherapistService.findByEmail(email);
      if (!user) {
        return res
          .status(404)
          .json({ success: false, message: "No user found." });
      }

      if (user.isDeleted) {
        return res
          .status(400)
          .json({ success: false, message: "Account Blocked." });
      }
      if (!Utility.comparePasswordHash(user?.password, req.body.password)) {
        return res
          .status(400)
          .json({ success: false, message: "Invalid credentials." });
      }
      let token = Utility.generateJwtToken(user?._id);
      res.send(
        new Response(
          { token, user: user?.email },
          "Therapist Login successful",
          200
        )
      );
    } catch (err) {
      next(err);
    }
  }

  static async addEventToCalendar(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const data = req.body;
      const googleCalendarData = await GoogleCalendarService.findByTherapist(
        therapistId
      );

      if (!googleCalendarData) {
        return res
          .status(404)
          .json({ success: false, message: "No Google Calender Data found." });
      }

      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );

      const tokens = {
        access_token: googleCalendarData.access_token,
        refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
      };

      // Assuming you're storing tokens in session after authentication
      if (tokens) {
        oauth2Client.setCredentials(tokens);

        const calendar: any = google.calendar({
          version: "v3",
          auth: oauth2Client,
        });

        const event = {
          summary: data.summary,
          location: data.location,
          description: data.description,
          visibility: "private", // Set the event visibility to private
          start: {
            dateTime: data.start.dateTime,
            timeZone: data.start.timeZone,
          },
          end: {
            dateTime: data.end.dateTime,
            timeZone: data.end.timeZone,
          },
          attendees: data.emails, // [{ email: '<EMAIL>' }, { email: '<EMAIL>' }]
          conferenceData: {
            createRequest: {
              requestId: uuidv4(), // Generate a random string for the requestId
              conferenceSolutionKey: {
                type: "hangoutsMeet", // Use 'hangoutsMeet' for Google Meet
              },
            },
          },
        };

        const createdEvent = await calendar.events.insert({
          calendarId: "primary",
          resource: event,
          conferenceDataVersion: 1, // Required to include conferenceData
          sendNotifications: true, // If you want to send email notifications to attendees
          requestBody: event, // The event details
        });

        res.send(`Event created: ${createdEvent.data.htmlLink}`);
      } else {
        throw new Error("Authentication tokens are not available");
      }
    } catch (error) {
      next(error);
    }
  }

  static async getCalendarEvents(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const therapist = req.therapist;

      const maxResults: any = req.query.maxResults; // pass maxResults in query to fetch no. of events.

      const googleCalendarData = await GoogleCalendarService.findByTherapist(
        therapistId
      );
      if (!googleCalendarData) {
        return res
          .status(404)
          .json({ success: false, message: "No Google Calender Data found." });
      }

      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );

      const tokens = {
        access_token: googleCalendarData.access_token,
        refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
      };

      // Assuming you're storing tokens in session after authentication
      if (tokens) {
        oauth2Client.setCredentials(tokens);

        const calendar = google.calendar({ version: "v3", auth: oauth2Client });

        const calendarResponse = await calendar.events.list({
          calendarId: "primary",
          timeMin: new Date().toISOString(),
          maxResults: maxResults || 500, // Change this value if you want to fetch more or fewer events
          singleEvents: true,
          orderBy: "startTime",
        });
        const events: any = calendarResponse.data.items;
        if (events.length == 0) {
          therapist.googleCalendarSynced = true;
          therapist.gCalLastSyncOn = new Date();
          await therapist.save();
        }
        if (events.length > 0) {
          const iCalUIDs: any = events.map((event: any) => event.iCalUID);
          const uniqueICalUIDs = Array.from(new Set(iCalUIDs));
          let data = [];
          for (let iCalUid of uniqueICalUIDs) {
            let event = events.find((event: any) => event.iCalUID == iCalUid);
            let eventIds = events
              .filter((event: any) => event.iCalUID == iCalUid)
              .map((event: any) => event.id);
            const eventOccurences = events.filter(
              (event: any) => event.iCalUID == iCalUid
            ).length;
            event.eventOccurences = eventOccurences;
            event.eventIds = eventIds;
            data.push(event);
          }
          const syncable = data.filter(
            (eve) => eve.attendees && eve.attendees.length > 1
          );
          const not_syncable = data.filter((eve) => !eve.attendees);

          // res.json(events);
          res.json({ syncable, not_syncable });
        } else {
          res
            .status(404)
            .json({ success: false, message: "No upcoming events found." });
        }
      } else {
        throw new Error("Authentication tokens are not available");
      }
    } catch (error) {
      next(error);
    }
  }

  static async getScheduleList(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const schedules = await ScheduleService.getScheduleListByFilter(
        {
          therapistId: req.therapist._id,
        },
        {},
        { sort: { createdAt: -1 } }
      );
      res.send({ syncable: schedules, not_syncable: [] });
    } catch (error) {
      next(error);
    }
  }

  static async getSpecificCalendarEvents(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const schedules = await GoogleCalendarService.syncCalendarLocally(
        req.therapist._id,
        req.body.eventIds
      );
      if (!schedules) {
        return res.status(400).send("Unable to fetch schedules.");
      }

      res.send({ schedules: schedules });
    } catch (error) {
      // The error handler middleware will handle conflicts with the conflicts array
      next(error);
    }
  }

  static async getSpecificCalendarEventClients(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const eventsWithClient =
        await GoogleCalendarService.syncCalendarClientsLocally(
          req.therapist._id,
          req.body.events
        );
      if (!eventsWithClient) {
        return res.status(400).send("Unable to fetch events client data.");
      }
      if (!eventsWithClient.success) {
        return res.status(400).send(eventsWithClient.message);
      }

      res.send({ eventsWithClient });
    } catch (error) {
      next(error);
    }
  }

  static async getTherapist(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.params.id;
      const therapist = await TherapistService.getTherapist(therapistId);
      if (!therapist) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to get therapist." });
      }
      res.send(therapist);
    } catch (error) {
      next(error);
    }
  }

  static async getCountAll(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const isVerified = req.query.isVerified;

      let count = await TherapistService.getAllCount(isVerified);

      res.send({ count: count });
    } catch (error) {
      next(error);
    }
  }

  static async getAll(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const therapistId: any = req.query.therapist;
      const isVerified = req.query.isVerified;

      const fromDate: any = req.query.fromDate;
      const toDate: any = req.query.toDate;

      let startDate: any;
      let endDate: any;
      if (fromDate && toDate) {
        startDate = moment(fromDate, "YYYY-MM-DD").startOf("day");
        endDate = moment(toDate, "YYYY-MM-DD").endOf("day");
      }

      let therapists: any = await TherapistService.getAll(
        pageSize,
        skip,
        therapistId,
        isVerified
      );
      if (!therapists) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to get therapists." });
      }

      for (let therapist of therapists) {
        let allRecurrenceDates: any = [];
        let cancelledSchedules = [];
        let upcomingSchedules = [];
        let totalSchedulesWithFilter;
        let totalSchedules;
        let totalCollection = 0;

        if (fromDate && toDate) {
          const schedules = await ScheduleDao.getAllRecurrenceDatesByTherapist(
            therapist._id
          );
          if (schedules.length > 0) {
            for (let schedule of schedules) {
              allRecurrenceDates.push(schedule.recurrenceDates);
            }
            const reqRecurrenceDates: any = [].concat(...allRecurrenceDates);
            totalSchedules = reqRecurrenceDates;
            const schedulesWithDateRange = await reqRecurrenceDates.filter(
              (item: any) => {
                const toDate = moment(item.toDate);
                return toDate.isBetween(startDate, endDate, null, "[]"); // '[]' includes both start and end
              }
            );

            totalSchedulesWithFilter = schedulesWithDateRange;
            for (let scheduleWithDateRange of schedulesWithDateRange) {
              if (scheduleWithDateRange.status == ScheduleStatus.CANCELLED) {
                cancelledSchedules.push(scheduleWithDateRange);
              }
              if (
                moment(scheduleWithDateRange.toDate) > moment() &&
                scheduleWithDateRange.status != ScheduleStatus.CANCELLED
              ) {
                upcomingSchedules.push(scheduleWithDateRange);
              }
            }

            const completedTransactions =
              await TransactionDao.getCompletedWithDate(
                therapist._id,
                startDate.toISOString(),
                endDate.toISOString()
              );
            totalCollection = completedTransactions.reduce(
              (total, current) => total + Number(current.amount),
              0
            );
          }
        }

        therapist.upcomingSessions = upcomingSchedules.length;
        therapist.totalSessions = totalSchedules?.length ?? 0;
        therapist.totalSessionsWithFilter =
          totalSchedulesWithFilter?.length ?? 0;
        therapist.cancelledSessions = cancelledSchedules.length;
        therapist.totalCollection = totalCollection ?? 0;
      }

      res.send(therapists);
    } catch (error) {
      next(error);
    }
  }

  static async getTherapistData(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      let therapist: any = await TherapistDao.getLeanTherapist(therapistId);
      if (!therapist) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to get therapist data." });
      }

      let profile;

      if (therapist?.s3ProfilePhoto) {
        profile = await FileDownloadService.downloadFile(String(therapistId));
        therapist = await {
          ...therapist,
          profilePhoto: "data:image/png;base64," + profile,
        };
      }

      // if (therapist?.verificationDetails?.uploadedDocsCount > 0) {
      //     let docData = []
      //     for (let docCount = 1; docCount <= therapist?.verificationDetails?.uploadedDocsCount; docCount++) {
      //         const downloadedDocument = await FileDownloadService.downloadFile(String(therapistId) + "_doc_" + docCount)
      //         const document = "data:image/png;base64," + downloadedDocument;
      //         await docData.push(document)
      //     }
      //     therapist = await {
      //         ...therapist,
      //         documents: docData
      //     }
      // }
      res.send({ therapist: therapist });
    } catch (error) {
      next(error);
    }
  }

  static async getTherapistVerificatonData(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const therapist = await TherapistService.getTherapistData(therapistId);
      if (!therapist) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to get therapist data." });
      }
      res.send({ therapistId, therapist });
    } catch (error: any) {
      next(error);
    }
  }

  static async updateTherapist(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.params.id;
      const therapistData = req.body;
      const therapist = await TherapistService.updateTherapist(
        therapistId,
        therapistData
      );
      if (!therapist) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to update therapist." });
      }
      res.send(therapist);
    } catch (error) {
      next(error);
    }
  }

  static async updateTherapistData(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      let updatedTherapistData = req.body;
      if (updatedTherapistData.documents) {
        delete updatedTherapistData.documents;
      }
      if (!updatedTherapistData.oldPassword && updatedTherapistData.password) {
        const hashPassword = await Utility.createPasswordHash(
          updatedTherapistData.password
        );
        updatedTherapistData.password = hashPassword;
      }
      if (updatedTherapistData.oldPassword && updatedTherapistData.password) {
        const isOldPasswordCorrect = await Utility.comparePasswordHash(
          req.therapist.password,
          updatedTherapistData.oldPassword
        );
        if (!isOldPasswordCorrect) {
          return res
            .status(500)
            .json({ success: false, message: "Old Password is incorrect." });
        }
        delete updatedTherapistData.oldPassword;
        const hashPassword = await Utility.createPasswordHash(
          updatedTherapistData.password
        );
        updatedTherapistData.password = hashPassword;
      }
      const therapist = await TherapistService.updateTherapistData(
        therapistId,
        updatedTherapistData
      );
      if (!therapist) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to update therapist." });
      }
      res.send("Upadated successfully.");
    } catch (error) {
      next(error);
    }
  }

  static async deleteTherapist(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.params.id;
      const therapist = await TherapistService.deleteTherapist(therapistId);
      if (!therapist) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to delete therapist." });
      }
      res.send(therapist);
    } catch (error) {
      next(error);
    }
  }

  static async testingBrevo(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      let senderData = {
        email: "",
        name: "",
      };
      let receiverData = [
        {
          email: "",
          name: "There",
          phone: "+8239049605",
        },
      ];
      let subject = MailSubjectEnum.TESTING;
      let htmlTemplate = testingTemplate();

      const sentEmail = await Mailer2.sendMail(
        senderData,
        receiverData,
        subject,
        htmlTemplate
      );

      if (!sentEmail) {
        return res.status(400).json({ success: false, message: "Failed" });
      }
      res.send({ sentEmail });
    } catch (error) {
      next(error);
    }
  }

  static async addTherapist(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );
      const code: any = req.query.code;
      const { tokens } = await oauth2Client.getToken(code);
      if (!tokens.access_token) {
        return res.status(400).json({ error: "Access token is missing." });
      }
      const tokenInfo = await oauth2Client.getTokenInfo(tokens.access_token);
      const grantedScopes = tokenInfo.scopes || [];

      // Check if all required scopes are granted
      const missingScopes = scopes.filter(
        (scope) => !grantedScopes.includes(scope)
      );

      if (missingScopes.length > 0) {
        return res.status(403).json({
          error: "You must grant all required permissions.",
          missingScopes,
        });
      }
      const userData = await GoogleRequestService.makeRequest(
        "/oauth2/v2/userinfo",
        "GET",
        tokens.access_token
      );
      // const allCalendarData = await GoogleRequestService.makeRequest("/calendar/v3/users/me/calendarList", "GET", tokens.access_token);
      // for (let calendar of allCalendarData.items) {
      //     const calendarById = await GoogleRequestService.makeRequest("/calendar/v3/calendars/" + calendar.id, "GET", tokens.access_token);
      // }

      const keyToEncryptDataOfTherapist =
        await SymmetricCryptService.createRandomKey();
      const existingTherapist = await TherapistService.findByEmail(
        userData.email
      );
      if (existingTherapist?.isDeleted) {
        return res
          .status(403)
          .json({ success: false, message: "Account Blocked" });
      }
      if (!existingTherapist) {
        const therapist = await TherapistService.createTherapist({
          email: userData.email,
          name: userData.name,
        });
        if (!therapist) {
          return res
            .status(400)
            .json({ success: false, message: "Unable to create therapist." });
        }
        const existingGoogleCalendar: any =
          await GoogleCalendarService.findByTherapist(therapist._id);
        if (!existingGoogleCalendar) {
          const googleCalendar = await GoogleCalendarService.create({
            access_token: tokens.access_token,
            refresh_token: tokens.refresh_token,
            access_code: req.query.code,
            therapist: therapist._id,
          });
          if (!googleCalendar) {
            return res.status(400).json({
              success: false,
              message: "Unable to create google calendar creds.",
            });
          }
        }
        const existingKeyForTherapist = await KeysDao.findByTherapist(
          therapist._id
        );
        if (!existingKeyForTherapist) {
          await KeysDao.createKey(keyToEncryptDataOfTherapist, therapist._id);
        }
        let token = Utility.generateJwtToken(therapist?._id);
        return res.status(200).json({
          data: CONFIG.frontendBaseUrl,
          token,
          therapistId: therapist._id,
          isVerified: therapist.isVerified,
          newUser: true,
          message: "token created successfully",
        });
      }
      const existingGoogleCalendar: any =
        await GoogleCalendarService.findByTherapist(existingTherapist._id);
      await GoogleCalendarService.update(existingGoogleCalendar._id, {
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        access_code: req.query.code,
      });
      let token = Utility.generateJwtToken(existingTherapist?._id);
      res.status(200).json({
        data: CONFIG.frontendBaseUrl,
        token,
        therapistId: existingTherapist._id,
        isVerified: existingTherapist.isVerified,
        therapist_name: existingTherapist?.therapist_name || "",
        message: "token created successfully",
      });
    } catch (error) {
      next(error);
    }
  }

  static async getSchedulesCount(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      let startDate: any = req.query.startDate
        ? String(req.query.startDate)
        : undefined;
      let endDate: any = req.query.endDate
        ? String(req.query.endDate)
        : undefined;
      const therapistId = req.therapist._id;

      if (!startDate || !endDate) {
        return res.status(400).json({
          success: false,
          message: "Start date and end date are required",
        });
      }

      startDate = moment(startDate).toISOString();
      endDate = moment(endDate).toISOString();

      let stats = {
        completed_sessions: 0,
        rescheduled_sessions: 0,
        cancelled_sessions: 0,
      };

      const sessions = await ScheduleModel.find(
        {
          therapistId: therapistId,
          "recurrenceDates.fromDate": {
            $gte: new Date(startDate),
            $lte: new Date(endDate),
          },
        },
        "_id recurrenceDates"
      );

      for (let session of sessions) {
        for (let recurrence of session.recurrenceDates) {
          if (moment(recurrence.fromDate).isBetween(startDate, endDate)) {
            if (recurrence.status == ScheduleStatus.COMPLETED) {
              stats.completed_sessions += 1;
            }
            if (recurrence.status == ScheduleStatus.RESCHEDULED) {
              stats.rescheduled_sessions += 1;
            }
            if (recurrence.status == ScheduleStatus.CANCELLED) {
              stats.cancelled_sessions += 1;
            }
          }
        }
      }

      res.send({ stats });

      // let upcomingWeekSchedules = [];
      // for(let reqRecurrenceDate of reqRecurrenceDates){
      //     if(reqRecurrenceDate.status == "cancelled"){
      //         cancelledSchedules.push(reqRecurrenceDate)
      //     }
      //     const isBeforeOneWeek = moment(reqRecurrenceDate.toDate).isBefore(moment().add(1, "week"))
      //     if(isBeforeOneWeek){
      //         upcomingWeekSchedules.push(reqRecurrenceDate)
      //     }
      // }

      // res.send({appointments: reqRecurrenceDates.length, cancelledSchedules: cancelledSchedules.length, upcomingWeekSchedules: upcomingWeekSchedules.length})
    } catch (error) {
      next(error);
    }
  }

  // static async getSchedules(
  //   req: express.Request,
  //   res: express.Response,
  //   next: express.NextFunction
  // ) {
  //   try {
  //     const pageNumber: any = Number(req.query.pageNumber) || 1;
  //     const pageSize: any = Number(req.query.pageSize) || 20;
  //     const skip = (pageNumber - 1) * pageSize;
  //     const status = req.query.status;
  //     // const searchText = String(req.query.searchText);
  //     let searchText = undefined;
  //     if (req.query.searchText != undefined) {
  //       searchText = String(req.query.searchText);
  //     }
  //     const therapistId = req.therapist._id;
  //     const clientId = req.query.client;
  //     const startDate: any = req.query.startDate;
  //     const endDate: any = req.query.endDate;
  //     const name = req.query.name;
  //     let count;
  //     const schedules = await ScheduleDao.getAllRecurrenceDatesByTherapist(
  //       therapistId,
  //       undefined,
  //       clientId,
  //       searchText
  //     );
  //     let allRecurrenceDates: any = [];
  //     for (let schedule of schedules) {
  //       allRecurrenceDates.push(schedule.recurrenceDates);
  //     }
  //     const reqRecurrenceDates: any = [].concat(...allRecurrenceDates);
  //     const latestRecurrenceDates = reqRecurrenceDates.sort(
  //       (a: any, b: any) => a.toDate - b.toDate
  //     );
  //     let reqData = [];
  //     for (let recurrenceDate of latestRecurrenceDates) {
  //       const schedule: any = await ScheduleDao.getByRecurrenceDateId(
  //         recurrenceDate._id
  //       );
  //       if (schedule?.clientId?.isActive) {
  //         const scheduleData = {
  //           _id: schedule?._id,
  //           email: schedule?.email,
  //           additionalEmails: schedule?.additionalEmails,
  //           scheduleId: schedule?.scheduleId,
  //           name: schedule?.name,
  //           fromDate: recurrenceDate?.fromDate,
  //           toDate: recurrenceDate?.toDate,
  //           status: recurrenceDate?.status,
  //           therapistId: schedule?.therapistId,
  //           clientCountry: schedule?.clientCountry,
  //           recurrence: schedule?.recurrence,
  //           isActive: schedule?.isActive,
  //           syncWithCalender: schedule?.syncWithCalender,
  //           tillDate: schedule?.tillDate,
  //           description: schedule?.description,
  //           createdAt: schedule?.createdAt,
  //           updatedAt: schedule?.updatedAt,
  //           recurrenceDateId: recurrenceDate._id,
  //           phone: schedule?.phone,
  //           amount: recurrenceDate?.amount,
  //           transactionId: recurrenceDate?.transactionId,
  //           meetLink: recurrenceDate?.meetLink,
  //           client: {
  //             name: schedule?.clientId?.name,
  //             email: schedule?.clientId?.email,
  //             isActive: schedule?.clientId?.isActive,
  //             _id: schedule?.clientId?._id,
  //           },
  //         };
  //         reqData.push(scheduleData);
  //       }
  //     }
  //     let paginationData: any;
  //     if (status) {
  //       if (status == "upcoming") {
  //         const upcomingSessions = await reqData.filter(
  //           (schedule) =>
  //             schedule.toDate.toISOString() > new Date().toISOString() &&
  //             (schedule.status == ScheduleStatus.PENDING ||
  //               schedule.status == ScheduleStatus.CONFIRMED ||
  //               schedule.status == ScheduleStatus.RESCHEDULED)
  //         );
  //         let sessions = await ScheduleService.getSchedules(
  //           upcomingSessions,
  //           skip,
  //           pageSize,
  //           name,
  //           startDate,
  //           endDate
  //         );
  //         paginationData = sessions?.paginationData;
  //         count = sessions?.count;
  //       }
  //       if (status == ScheduleStatus.COMPLETED) {
  //         const completedSessions = reqData.filter(
  //           (schedule) => schedule.status == ScheduleStatus.COMPLETED
  //         );
  //         let sessions = await ScheduleService.getSchedules(
  //           completedSessions,
  //           skip,
  //           pageSize,
  //           name,
  //           startDate,
  //           endDate
  //         );
  //         paginationData = sessions?.paginationData;
  //         count = sessions?.count;
  //       }
  //       if (status == ScheduleStatus.CANCELLED) {
  //         const cancelledSessions = reqData.filter(
  //           (schedule) => schedule.status == ScheduleStatus.CANCELLED
  //         );
  //         let sessions = await ScheduleService.getSchedules(
  //           cancelledSessions,
  //           skip,
  //           pageSize,
  //           name,
  //           startDate,
  //           endDate
  //         );
  //         paginationData = sessions?.paginationData;
  //         count = sessions?.count;
  //       }
  //     }
  //     if (!status) {
  //       const sessions = await ScheduleService.getSchedules(
  //         reqData,
  //         skip,
  //         pageSize,
  //         name,
  //         startDate,
  //         endDate
  //       );
  //       count = sessions?.count;
  //       paginationData = sessions?.paginationData;
  //     }
  //     res.send({ appointments: paginationData, appointmentsCount: count });
  //   } catch (error) {
  //     next(error);
  //   }
  // }

  static async getSchedules(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber = Number(req.query.pageNumber) || 1;
      const pageSize = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const therapistId = req.therapist._id;

      // Extract query parameters with proper types
      const status = req.query.status as string;
      const searchText = req.query.searchText;
      const clientId = req.query.client as string;
      const startDate = req.query?.startDate;
      const endDate = req.query?.endDate;
      const name = req.query.name as string;
      const fromPublicCalender = req.query?.fromPublicCalender as string;

      // Consolidate and optimize data fetching with filtering at database level
      let schedules = await ScheduleDao.getAllRecurrenceDatesByTherapist(
        therapistId,
        status,
        clientId,
        searchText,
        startDate,
        endDate,
        // skip,
        // pageSize
        fromPublicCalender
      );
      // schedules = schedules.filter((elem: any) => {
      //   return elem.clientId.isActive;
      // });
      const count = schedules?.length || 0;
      schedules = schedules.slice(skip, skip + pageSize);

      // Transform the data for the response
      // const reqData = schedules.map((schedule: any) => ({
      //   _id: schedule._id,
      //   email: schedule.email,
      //   additionalEmails: schedule.additionalEmails,
      //   scheduleId: schedule.scheduleId,
      //   name: schedule.name,
      //   fromDate: schedule.recurrenceDates.fromDate,
      //   toDate: schedule.recurrenceDates.toDate,
      //   status: schedule.recurrenceDates.status,
      //   therapistId: schedule.therapistId,
      //   clientCountry: schedule.clientCountry,
      //   recurrence: schedule.recurrence,
      //   isActive: schedule.isActive,
      //   syncWithCalender: schedule.syncWithCalender,
      //   tillDate: schedule.tillDate,
      //   description: schedule.description,
      //   createdAt: schedule.createdAt,
      //   updatedAt: schedule.updatedAt,
      //   recurrenceDateId: schedule.recurrenceDates._id,
      //   phone: schedule.phone,
      //   amount: schedule.recurrenceDates.amount,
      //   transactionId: schedule.recurrenceDates.transactionId,
      //   meetLink: schedule.recurrenceDates.meetLink,
      //   client: {
      //     name: schedule.clientId?.name,
      //     email: schedule.clientId?.email,
      //     isActive: schedule.clientId?.isActive,
      //     _id: schedule.clientId?._id,
      //   },
      // }));

      // const reqData = schedules.flatMap((schedule: any) => {
      //   return schedule.recurrenceDates?.map((recurrence: any) => ({
      //     _id: schedule._id,
      //     email: schedule.email,
      //     additionalEmails: schedule.additionalEmails,
      //     scheduleId: schedule.scheduleId,
      //     name: schedule.name,
      //     fromDate: recurrence.fromDate,
      //     toDate: recurrence.toDate,
      //     status: recurrence.status,
      //     therapistId: schedule.therapistId,
      //     clientCountry: schedule.clientCountry,
      //     recurrence: schedule.recurrence,
      //     isActive: schedule.isActive,
      //     syncWithCalender: schedule.syncWithCalender,
      //     tillDate: schedule.tillDate,
      //     description: schedule.description,
      //     createdAt: schedule.createdAt,
      //     updatedAt: schedule.updatedAt,
      //     recurrenceDateId: recurrence._id,
      //     phone: schedule.phone,
      //     amount: recurrence.amount,
      //     transactionId: recurrence.payTrackerId,
      //     meetLink: recurrence.meetLink,
      //     client: {
      //       name: schedule.clientId?.name,
      //       email: schedule.clientId?.email,
      //       isActive: schedule.clientId?.isActive,
      //       _id: schedule.clientId?._id,
      //     },
      //   }));
      // });

      // Pagination Data
      // const count = await ScheduleDao.countSchedules(
      //   therapistId,
      //   status,
      //   clientId,
      //   searchText,
      //   startDate,
      //   endDate
      // );
      const paginationData = {
        total: count,
        currentPage: pageNumber,
        totalPages: Math.ceil(count / pageSize),
        data: schedules,
      };

      // Send the response
      res.send({
        appointments: schedules,
        appointmentsCount: count,
        pageNumber: pageNumber,
        pageSize: pageSize,
      });
    } catch (error) {
      next(error);
    }
  }

  static async createSchedule(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      req.body.therapistId = req.therapist._id;
      if (req.therapist.isVerified === false) {
        return res
          .status(400)
          .json({ success: false, message: "Therapist not verified." });
      }

      let {
        emails,
        clientCountry,
        fromDate,
        sessionDate,
        toDate,
        name,
        recurrence,
        tillDate,
        description,
        phone,
        location,
        gender,
        age,
        summary,
        amount,
        isBefore,
        therapistId,
        timezone,
      } = req.body;

      // 3 months standard/fixed duration
      tillDate = moment().add(3, "months");

      fromDate = moment(mergeDate(sessionDate, fromDate));
      let toEndDate = moment(mergeDate(sessionDate, toDate));

      // toDate = moment(toDate);
      let endDate = moment(fromDate).add(3, "months");

      const duration = toEndDate.diff(fromDate, "minutes");

      if (duration <= 0) {
        return res.status(400).json({
          success: false,
          message: "Select a valid Time for Appointment Duration",
        });
      }

      // get and check for dates overlap
      let recurrenceDates: any = [];
      switch (recurrence) {
        case "Every Day": {
          recurrenceDates = createDailyRecurrences(
            fromDate,
            toDate,
            endDate,
            tillDate,
            1,
            "days",
            amount,
            recurrence
          );
          break;
        }

        case "Every Week On Monday":
        case "Every Week On Tuesday":
        case "Every Week On Wednesday":
        case "Every Week On Thursday":
        case "Every Week On Friday":
        case "Every Week On Saturday":
        case "Every Week On Sunday": {
          recurrenceDates = createDailyRecurrences(
            fromDate,
            toDate,
            endDate,
            tillDate,
            1,
            "weeks",
            amount,
            recurrence
          );
          break;
        }

        case "Every Two Weeks Monday":
        case "Every Two Weeks Tuesday":
        case "Every Two Weeks Wednesday":
        case "Every Two Weeks Thursday":
        case "Every Two Weeks Friday":
        case "Every Two Weeks Saturday":
        case "Every Two Weeks Sunday": {
          recurrenceDates = createDailyRecurrences(
            fromDate,
            toDate,
            endDate,
            tillDate,
            2,
            "weeks",
            amount,
            recurrence
          );
          break;
        }
        case "Does Not Repeat":
        default: {
          recurrenceDates.push({
            fromDate: fromDate,
            toDate: toEndDate,
            amount: amount,
            rrule: createRRule(recurrence, toEndDate),
          });
          break;
        }
      }
      const old_schedules = await ScheduleService.getScheduleByTherapistId(
        therapistId
      );
      // console.log({ old_schedules });
      let existing_dates = [];
      for (const old_schedule of old_schedules) {
        for (const old_date of old_schedule.recurrenceDates) {
          if (old_date.status != ScheduleStatus.CANCELLED) {
            existing_dates.push({
              fromDate: moment(old_date.fromDate),
              toDate: moment(old_date.toDate),
            });
          }
        }
      }
      // console.log({ existing_dates });
      // const checkifOverlap = findOverlapDate(recurrenceDates, existing_dates);

      // if (checkifOverlap && checkifOverlap.length > 0) {
      //   return res.status(400).json({
      //     success: false,
      //     message: `Appointment already exist for ${checkifOverlap}`,
      //   });
      // }

      const checkifOverlap = isOverlap(recurrenceDates, existing_dates);

      if (checkifOverlap) {
        return res.status(400).json({
          success: false,
          message: `Appointment already exist`,
        });
      }

      let isEvent = false;
      for (const recurrence of recurrenceDates) {
        const event = await GoogleCalendarService.eventByDate(
          req.therapist._id,
          200,
          recurrence.fromDate,
          recurrence.toDate
        );
        if (event.length > 0) {
          isEvent = true;
          break;
        }
      }
      if (isEvent) {
        return res.status(400).json({
          success: false,
          message:
            "This session is already added to your Google Calendar. Please check your calendar to avoid duplicate entries.",
        });
      }

      // get or create client if not exist
      let client: any = undefined;
      for (let email of emails) {
        const clientExist = await ClientService.getClientByEmail(
          email,
          therapistId
        );
        if (clientExist) {
          client = clientExist;
          break;
        }
      }

      if (client) {
        if (client.isActive === false) {
          return res
            .status(400)
            .json({ success: false, message: "Client is not active." });
        }
      }

      if (!client) {
        client = await ClientService.create({
          email: emails[0],
          name: name,
          therapistId: therapistId,
          phone: phone,
          gender: gender,
          age: age,
          defaultSessionAmount: String(amount),
          defaultTimezone: timezone,
        });
        if (!client) {
          return res.status(404).json({
            success: false,
            message: "client not created. Something went wrong.",
          });
        }
      } else {
        client.name = name;
        client.phone = phone;
        client.gender = gender;
        client.age = age;
        await client.save();
      }

      const scheduleData: ICreateScheduleData = {
        clientId: client._id,
        additionalEmails: emails.filter(
          (email: string) => email != client.email
        ), // other clients
        duration: duration,
        clientCountry: clientCountry,
        recurrence: recurrence,
        tillDate: tillDate,
        description: description,
        recurrenceDates: recurrenceDates,
        location: location,
        age: age,
        gender: gender,
        summary: summary,
        paymentIsBefore: isBefore,
        fromPublicCalender: false,
      };
      const schedule = await ScheduleService.create(therapistId, scheduleData);

      if (!schedule) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to create schedule." });
      }

      client.defaultTimezone = timezone;
      await client.save();

      let recurrenceDate = schedule.recurrenceDates[0];

      const addEventPayload: IAddEventPayload = {
        emails: emails,
        summary: summary,
        location: location,
        description: description,
      };

      const recurranceData = {
        fromDate: recurrenceDate.fromDate,
        toDate: recurrenceDate.toDate,
        _id: recurrenceDate._id,
        rrule: recurrenceDates[0].rrule,
        data: schedule.recurrenceDates,
      };

      let googleCalenderEvent: GoogleCalendarEvent = {
        link: "",
        calenderEventId: [],
      };
      if (location === "online") {
        googleCalenderEvent = await GoogleCalendarService.addEventToCalender(
          therapistId,
          addEventPayload,
          recurranceData,
          schedule._id
        );
      }
      // const paymentLink = await StripeService.createPaymentLink(amount, req.therapist, schedule._id, client._id, recurrenceDate._id);
      let paymentLink: any;
      let payTrackerId = undefined;
      let updateRecurrenceDate: any = {};
      for (const event of googleCalenderEvent.calenderEventId) {
        if (req.therapist.menus.paymentGateway) {
          if (!req.body.collectYourself) {
            paymentLink = await RazorpayService.createPaymentLink(
              amount,
              req.therapist._id,
              schedule._id,
              event.scheduleRecId,
              client._id
            );
            if (!paymentLink) {
              return res.status(400).json({
                success: false,
                message: "Unable to create payment link.",
              });
            }
          }
        }
        if (req.therapist.menus.paymentTracker) {
          const paymentTracker = await PayTrackerService.createPayTracker({
            therapistId: req.therapist._id,
            scheduleId: event?.scheduleId,
            scheduleRecId: event.scheduleRecId,
            clientId: client._id,
            dueDate: event.start?.dateTime || "",
            amount: {
              currency: "INR",
              value: recurrenceDate.amount,
            },
            paymentType: isBefore
              ? PaymentTrackerTypeEnum.Advance
              : PaymentTrackerTypeEnum.Post_Session,
            status: PaymentTrackerStatusEnum.Still_Pending,
            paymentDate: undefined,
            isDeleted: false,
            tags: [],
            sendRemainder: 0,
            isFine: false,
            cancellationFee: {
              currency: "INR",
              value: 0,
            },
          });

          payTrackerId = paymentTracker._id;
        }
        updateRecurrenceDate = await ScheduleService.updateRecurrenceDate(
          event?.scheduleId || "",
          event?.scheduleRecId || "",
          paymentLink?.transactionId || undefined,
          googleCalenderEvent?.link || "",
          event?._id || "",
          payTrackerId
        );
        if (!updateRecurrenceDate) {
          return res.status(400).json({
            success: false,
            message: "Unable to update recurrence date.",
          });
        }
      }

      const scheduleAfterCreated: any =
        await ScheduleService.getScheduleByTherapistAndClientId(
          therapistId,
          scheduleData.clientId
        );
      const recentRecurrenceDate: any = await Utility.findNearestFromDate(
        updateRecurrenceDate.recurrenceDates
      );
      // let paymentLink: any;
      for (let email of emails) {
        let receiverData: any;
        const client = await ClientService.getClientByEmail(email, therapistId);
        if (!client) {
          continue;
        } else {
          receiverData = emails.map((email: any) => {
            return {
              email: email,
              name: client.name || "There",
            };
          });
        }

        let senderData = {
          email: req.therapist.email,
          name: req.therapist.name,
        };

        // let subject = scheduleData.summary;
        let subject = MailSubjectEnum.REMAINDER;
        let htmlTemplate;
        if (location === "online") {
          if (isBefore) {
            htmlTemplate = scheduleReminder({
              clientName: client.name || "There",
              therapistName: req.therapist.name,
              scheduleDate: recentRecurrenceDate.fromDate,
              meetingLink: recentRecurrenceDate.meetLink,
              // paymentLink: paymentLink?.paymentLink,
              // payLater: false,
              timezone: client.defaultTimezone,
              amount: amount,
            });
          }
          if (!isBefore) {
            htmlTemplate = scheduleReminder({
              clientName: client.name || "There",
              therapistName: req.therapist.name,
              scheduleDate: recentRecurrenceDate.fromDate,
              meetingLink: recentRecurrenceDate.meetLink,
              // paymentLink: paymentLink?.paymentLink,
              // payLater: true,
              timezone: client.defaultTimezone,
              amount: amount,
            });
          }
        }
        if (location === "offline") {
          if (isBefore) {
            htmlTemplate = scheduleReminder({
              clientName: client.name || "There",
              therapistName: req.therapist.name,
              scheduleDate: recentRecurrenceDate?.fromDate,
              meetingLink: undefined,
              // paymentLink: paymentLink?.paymentLink,
              // payLater: false,
              timezone: client.defaultTimezone,
              amount: amount,
            });
          }
          if (!isBefore) {
            htmlTemplate = scheduleReminder({
              clientName: client.name || "There",
              therapistName: req.therapist.name,
              scheduleDate: recentRecurrenceDate?.fromDate,
              meetingLink: undefined,
              // paymentLink: paymentLink?.paymentLink,
              // payLater: true,
              timezone: client.defaultTimezone,
              amount: amount,
            });
          }
        }
        const sentEmail = await Mailer2.sendMail(
          senderData,
          receiverData,
          subject,
          htmlTemplate
        );
        if (!sentEmail) {
          return res.status(400).json({ success: false, message: "Failed" });
        }
        const formattedDate = format(
          new Date(recentRecurrenceDate.fromDate),
          "dd-MM-yyyy"
        );
        const formattedTime = format(
          new Date(recentRecurrenceDate.fromDate),
          "HH:mm"
        );

        const params = {
          CLIENT_NAME: client.name,
          THERAPIST_NAME: req?.therapist?.name,
          SESSION_DATE: formattedDate,
          SESSION_TIME: formattedTime,
          MEETLINK: recentRecurrenceDate.meetLink,
        };

        await sendWhatsAppNotification(client, "new_session2", params);
      }
      return res.status(200).json({ success: true, message: "ok" });
    } catch (error) {
      next(error);
    }
  }

  static async getScheduleById(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const recurrenceDateId = req.params.id;
      const therapistId = req.therapist._id;
      const schedule: any = await ScheduleDao.getScheduleByRecurrenceDateId(
        therapistId,
        recurrenceDateId
      );
      let fromDate;
      let toDate;
      let status;
      let amount;
      let isRecurrence = false;
      let iCalUid;

      if (schedule?.recurrenceDates.length > 1) {
        isRecurrence = true;
      }

      if (schedule?.recurrenceDates && schedule.recurrenceDates.length > 0) {
        const calendarEvent = await CalendarEventDao.getEventByEventId(
          schedule.recurrenceDates[0].calenderEventId
        );
        iCalUid = calendarEvent?.iCalUID;

        const calenderEventsByCalUid = await CalendarEventDao.getByCalUid(
          iCalUid
        );
        if (calenderEventsByCalUid.length > 1) {
          isRecurrence = true;
        }

        for (let recurrenceDate of schedule?.recurrenceDates) {
          if (recurrenceDateId == recurrenceDate._id) {
            fromDate = recurrenceDate.fromDate;
            toDate = recurrenceDate.toDate;
            status = recurrenceDate.status;
            amount = recurrenceDate?.amount;
          }
        }
      }

      const paymentLink = await TransactionService.getTransactionByRecId(
        recurrenceDateId,
        therapistId
      );
      const scheduleData = {
        _id: schedule?._id,
        email: schedule?.email,
        additionalEmails: schedule?.additionalEmails,
        scheduleId: schedule?.scheduleId,
        name: schedule?.name,
        fromDate: fromDate,
        toDate: toDate,
        status: status,
        amount: amount,
        therapistId: schedule?.therapistId,
        clientCountry: schedule?.clientCountry,
        recurrence: schedule?.recurrence,
        isActive: schedule?.isActive,
        syncWithCalender: schedule?.syncWithCalender,
        tillDate: schedule?.tillDate,
        description: schedule?.description,
        createdAt: schedule?.createdAt,
        updatedAt: schedule?.updatedAt,
        phone: schedule?.phone,
        recurrenceDateId: recurrenceDateId,
      };
      res.send({
        appointment: scheduleData,
        paymentLink: paymentLink,
        isRecurrence: isRecurrence,
      });
    } catch (error) {
      next(error);
    }
  }

  static async reschedule(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const recurrenceDateId = req.params.id;
      const fromDate = moment(req.body.fromDate);
      const toDate = moment(req.body.toDate);
      const duration = toDate.diff(fromDate, "minutes");
      if (duration <= 0) {
        return res.status(400).json({
          success: false,
          message: "Appointment Duration should be greater than 0.",
        });
      }
      const requestedRecurrenceDates = [
        {
          fromDate: new Date(req.body.fromDate),
          toDate: new Date(req.body.toDate),
        },
      ];
      let allRecurrenceDates: any = [];
      const schedules = await ScheduleDao.getAllRecurrenceDates(therapistId);
      for (let schedule of schedules) {
        allRecurrenceDates.push(schedule.recurrenceDates);
      }
      const recurrenceDatesPresent: any = [].concat(...allRecurrenceDates);
      const datesWithoutCancelled = recurrenceDatesPresent.filter(
        (recDate: any) =>
          recDate.status != ScheduleStatus.CANCELLED &&
          String(recDate._id) != String(recurrenceDateId)
      );

      const existingRecurrenceDates = datesWithoutCancelled;

      const dbConflicts: { startTime: string; endTime: string }[] = [];

      // Loop through each existing date and check for overlap with requested dates
      for (const existingDate of existingRecurrenceDates) {
        // Skip the session being rescheduled
        if (String(existingDate._id) === String(recurrenceDateId)) {
          continue;
        }

        for (const requestedDate of requestedRecurrenceDates) {
          const isOverlap = doIntervalsOverlap(existingDate, requestedDate);

          if (isOverlap) {
            dbConflicts.push({
              startTime: moment(existingDate.fromDate).toISOString(),
              endTime: moment(existingDate.toDate).toISOString(),
            });
            break; // Break inner loop once we find an overlap for this existing date
          }
        }
      }

      // If we found conflicts in the database, return a 200 response with the conflicts array
      if (dbConflicts.length > 0) {
        // Format the first conflict for the message
        const firstConflict = dbConflicts[0];
        const conflictDate = moment(firstConflict.startTime).format(
          "YYYY-MM-DD h:mm A"
        );

        let errorMessage = "";

        if (dbConflicts.length === 1) {
          errorMessage = `A conflict was found. You have a session in your database on ${conflictDate}.`;
        } else {
          errorMessage = `Conflicts were found. You have ${dbConflicts.length} existing sessions in your database that overlap with the new session.`;
          errorMessage += ` For example, you have a session on ${conflictDate}.`;
        }

        // Create a user-friendly array of conflict dates
        const conflictDates = dbConflicts.map((conflict) => {
          const startTime = moment(conflict.startTime).format(
            "YYYY-MM-DD HH:mm"
          );
          const endTime = moment(conflict.endTime).format("HH:mm");
          return `${startTime} - ${endTime}`;
        });

        return res.status(200).json({
          success: false,
          message: errorMessage,
          conflicts: dbConflicts,
          totalConflicts: dbConflicts.length,
          hasConflicts: true,
          conflictDates: conflictDates,
        });
      }

      const schedule: any = await ScheduleDao.getScheduleByRecurrenceDateId(
        therapistId,
        recurrenceDateId
      );

      const recurrenceIdData: any = await schedule?.recurrenceDates.find(
        (recurrence: any) => String(recurrence._id) == String(recurrenceDateId)
      );

      let existedEvent = await GoogleCalendarService.eventByDate(
        req.therapist._id,
        200,
        recurrenceIdData.fromDate,
        recurrenceIdData.toDate
      );

      // Check for conflicts in Google Calendar

      const conflicts: { startTime: string; endTime: string }[] = [];

      for (const recurrence of requestedRecurrenceDates) {
        const events = await GoogleCalendarService.eventByDate(
          req.therapist._id,
          200,
          recurrence.fromDate,
          recurrence.toDate
        );

        // Filter out events that match the existing event (which would be the one we're rescheduling)
        const conflictingEvents = events.filter((newEvent: any) => {
          // Check if this is the event we're rescheduling
          const isExistingEvent = existedEvent.some(
            (existing: any) =>
              newEvent.id === existing.id &&
              newEvent.start.dateTime === existing.start.dateTime &&
              newEvent.end.dateTime === existing.end.dateTime &&
              newEvent.status === existing.status
          );

          // If it's the existing event, it's not a conflict
          const isConflict = !isExistingEvent;
          return isConflict;
        });

        // Add each conflicting event to our conflicts array
        for (const event of conflictingEvents) {
          conflicts.push({
            startTime: event.start.dateTime,
            endTime: event.end.dateTime,
          });
        }
      }

      // If we found conflicts in Google Calendar, return a 200 response with the conflicts array
      if (conflicts.length > 0) {
        // Format the first conflict for the message
        const firstConflict = conflicts[0];
        const conflictDate = moment(firstConflict.startTime).format(
          "YYYY-MM-DD h:mm A"
        );

        let errorMessage = "";

        if (conflicts.length === 1) {
          errorMessage = `A conflict was found. You have a session in your Google Calendar on ${conflictDate}.`;
        } else {
          errorMessage = `Conflicts were found. You have ${conflicts.length} existing sessions in your Google Calendar that overlap with the new session.`;
          errorMessage += ` For example, you have a session on ${conflictDate}.`;
        }

        // Create a user-friendly array of conflict dates
        const conflictDates = conflicts.map((conflict) => {
          const startTime = moment(conflict.startTime).format(
            "YYYY-MM-DD HH:mm"
          );
          const endTime = moment(conflict.endTime).format("HH:mm");
          return `${startTime} - ${endTime}`;
        });

        return res.status(200).json({
          success: false,
          message: errorMessage,
          conflicts: conflicts,
          totalConflicts: conflicts.length,
          hasConflicts: true,
          conflictDates: conflictDates,
        });
      }

      const log = await LogsModel.create({
        therapist: therapistId,
        comments: "Appointment Rescheduled",
        changes: {
          oldFromDate: recurrenceIdData?.fromDate,
          oldToDate: recurrenceIdData?.toDate,
          newFromDate: fromDate,
          newToDate: toDate,
          scheduleId: schedule?._id,
          scheduleRecId: recurrenceIdData?._id,
        },
      });
      if (recurrenceIdData.calenderEventId) {
        const removeEvent = await GoogleCalendarService.removeEventFromCalendar(
          therapistId,
          recurrenceIdData
        );
      }
      let emails = [];
      await emails.push(schedule.clientId.email);
      const addEventPayload = {
        emails: emails,
        summary: schedule.summary,
        location: schedule.location,
        description: schedule.description,
      };
      const newRecurrenceData = {
        fromDate: fromDate.toISOString(),
        toDate: toDate.toISOString(),
        _id: recurrenceIdData._id,
        rrule: createRRule("Every Day", moment(toDate)),
      };
      // const googleCalenderEvent: any =
      //   await GoogleCalendarService.addEventToCalender(
      //     therapistId,
      //     addEventPayload,
      //     newRecurrenceData,
      //     schedule._id
      //   );

      let googleCalenderEvent: GoogleCalendarEvent = {
        link: "",
        calenderEventId: [],
      };
      if (schedule.location === "online") {
        googleCalenderEvent = await GoogleCalendarService.addEventToCalender(
          therapistId,
          addEventPayload,
          newRecurrenceData,
          schedule._id
        );
      }
      let updatedSchedule: any = {};
      for (const event of googleCalenderEvent.calenderEventId) {
        updatedSchedule = await ScheduleDao.reschedule(
          therapistId,
          recurrenceDateId,
          fromDate,
          toDate,
          googleCalenderEvent.link,
          event._id || ""
        );
        if (!updatedSchedule) {
          return res
            .status(400)
            .json({ success: false, message: "Unable to reschedule." });
        }
      }
      // let htmlTemplate = reScheduleEmail(
      //   schedule?.name || "There",
      //   req?.therapist?.name || "The Therapist",
      //   recurrenceIdData.fromDate,
      //   recurrenceIdData.toDate,
      //   fromDate,
      //   toDate,
      //   googleCalenderEvent.link,
      //   schedule?.clientId?.defaultTimezone || "Asia/Kolkata",
      //   recurrenceIdData?.amount || 0
      // );
      let htmlTemplate;
      if (schedule.location === "online") {
        htmlTemplate = reScheduleEmail(
          schedule?.name || "There",
          req?.therapist?.name || "The Therapist",
          recurrenceIdData.fromDate,
          recurrenceIdData.toDate,
          fromDate,
          toDate,
          googleCalenderEvent.link,
          schedule?.clientId?.defaultTimezone || "Asia/Kolkata",
          recurrenceIdData?.amount || 0
        );
      } else {
        htmlTemplate = reScheduleEmail(
          schedule?.name || "There",
          req?.therapist?.name || "The Therapist",
          recurrenceIdData.fromDate,
          recurrenceIdData.toDate,
          fromDate,
          toDate,
          undefined,
          schedule?.clientId?.defaultTimezone || "Asia/Kolkata",
          recurrenceIdData?.amount || 0
        );
      }
      let senderData = {
        email: req.therapist.email,
        name: req.therapist.name,
      };
      const receiverData = [
        {
          email: schedule.clientId.email,
          name: schedule?.clientId.name || "There",
        },
      ];
      let subject = MailSubjectEnum.APPOINTMENT_RESCHEDULE;
      const sentEmail = await Mailer2.sendMail(
        senderData,
        receiverData,
        subject,
        htmlTemplate
      );

      const formattedDate = format(
        new Date(recurrenceIdData.fromDate),
        "dd-MM-yyyy"
      );
      const formattedTime = format(
        new Date(recurrenceIdData.fromDate),
        "HH:mm"
      );

      const params = {
        CLIENT_NAME: schedule?.clientId.name,
        THERAPIST_NAME: req?.therapist?.name,
        SESSION_DATE: formattedDate,
        SESSION_TIME: formattedTime,
        MEETLINK: googleCalenderEvent.link,
      };

      await sendWhatsAppNotification(
        schedule?.clientId,
        "reschedule_session2",
        params
      );

      if (!sentEmail) {
        return res.status(400).json({ success: false, message: "Failed" });
      }

      // Create a copy of the arrays to avoid reference issues
      const dbConflictsCopy = [...(dbConflicts || [])];
      const conflictsCopy = [...(conflicts || [])];

      // No need to add test conflicts anymore

      // Combine database and Google Calendar conflicts
      const allConflicts = [...dbConflictsCopy, ...conflictsCopy];

      // Send the response with the appointment and conflicts
      const response = {
        appointment: updatedSchedule,
        hasConflicts: allConflicts.length > 0,
        conflicts: allConflicts,
        totalConflicts: allConflicts.length,
        dbConflicts: dbConflictsCopy,
        gcalConflicts: conflictsCopy,
      };

      res.send(response);
    } catch (error: any) {
      next(error);
    }
  }

  // static async reschedule(
  //   req: express.Request,
  //   res: express.Response,
  //   next: express.NextFunction
  // ) {
  //   try {
  //     const therapistId = req.therapist._id;
  //     const recurrenceDateId = req.params.id;
  //     const fromDate = moment(req.body.fromDate);
  //     const toDate = moment(req.body.toDate);
  //     const duration = toDate.diff(fromDate, "minutes");
  // const reschedule_session = req.body.reschedule_session;

  // if (!reschedule_session) {
  //   return res.status(400).json({
  //     success: false,
  //     message: "reschedule_session is required",
  //   });
  // }
  //     if (duration == 0) {
  //       return res.status(400).json({
  //         success: false,
  //         message: "Appointment Duration should be greater than 0.",
  //       });
  //     }
  //     const requestedRecurrenceDates = [
  //       {
  //         fromDate: new Date(req.body.fromDate),
  //         toDate: new Date(req.body.toDate),
  //       },
  //     ];
  //     let allRecurrenceDates: any = [];
  //     const schedules = await ScheduleDao.getAllRecurrenceDates(therapistId);
  //     for (let schedule of schedules) {
  //       allRecurrenceDates.push(schedule.recurrenceDates);
  //     }
  //     const recurrenceDatesPresent: any = [].concat(...allRecurrenceDates);
  //     const datesWithoutCancelled = recurrenceDatesPresent.filter(
  //       (recDate: any) =>
  //         recDate.status != ScheduleStatus.CANCELLED &&
  //         String(recDate._id) != String(recurrenceDateId)
  //     );
  //     let hasOverlap = false;
  //     const existingRecurrenceDates = datesWithoutCancelled;
  //     for (let requestedRecurrenceDate of requestedRecurrenceDates) {
  //       for (let existingRecurrenceDate of existingRecurrenceDates) {
  //         const intervalsOverlap = doIntervalsOverlap(
  //           existingRecurrenceDate,
  //           requestedRecurrenceDate
  //         );
  //         if (intervalsOverlap) {
  //           hasOverlap = true;
  //           break;
  //         }
  //       }
  //       if (hasOverlap) {
  //         break;
  //       }
  //     }
  //     if (hasOverlap) {
  //       return res
  //         .status(400)
  //         .json({ success: false, message: "Time Interval overlap." });
  //     }

  //     const schedule: any = await ScheduleDao.getScheduleByRecurrenceDateId(
  //       therapistId,
  //       recurrenceDateId
  //     );

  //     const recurrenceIdData: any = await schedule?.recurrenceDates.find(
  //       (recurrence: any) => String(recurrence._id) == String(recurrenceDateId)
  //     );
  //     const log = await LogsModel.create({
  //       therapist: therapistId,
  //       comments: "Appointment Rescheduled",
  //       changes: {
  //         oldFromDate: recurrenceIdData?.fromDate,
  //         oldToDate: recurrenceIdData?.toDate,
  //         newFromDate: fromDate,
  //         newToDate: toDate,
  //         scheduleId: schedule?._id,
  //         scheduleRecId: recurrenceIdData?._id,
  //       },
  //     });
  //     if (recurrenceIdData.calenderEventId) {
  //       const removeEvent = await GoogleCalendarService.removeEventFromCalendar(
  //         therapistId,
  //         recurrenceIdData
  //       );
  //     }
  //     let emails = [];
  //     await emails.push(schedule.clientId.email);
  //     const addEventPayload = {
  //       emails: emails,
  //       summary: schedule.summary,
  //       location: schedule.location,
  //       description: schedule.description,
  //     };
  //     const newRecurrenceData = {
  //       fromDate: fromDate.toISOString(),
  //       toDate: toDate.toISOString(),
  //       _id: recurrenceIdData._id,
  //     };
  //     const googleCalenderEvent: any =
  //       await GoogleCalendarService.addEventToCalender(
  //         therapistId,
  //         addEventPayload,
  //         newRecurrenceData,
  //         schedule._id
  //       );

  //     let updatedSchedule;

  // if (reschedule_session == "this session only") {
  //   updatedSchedule = await ScheduleDao.reschedule(
  //     therapistId,
  //     recurrenceDateId,
  //     fromDate,
  //     toDate,
  //     googleCalenderEvent.link,
  //     googleCalenderEvent.calenderEventId
  //   );
  // } else if (reschedule_session == "reschedule entire slot") {
  //   updatedSchedule = await ScheduleDao.allSchedule(
  //     therapistId,
  //     recurrenceDateId,
  //     schedule.clientId._id,
  //     fromDate,
  //     toDate,
  //     googleCalenderEvent.link,
  //     googleCalenderEvent.calenderEventId
  //   );
  // }
  //     if (!updatedSchedule) {
  //       return res
  //         .status(400)
  //         .json({ success: false, message: "Unable to reschedule." });
  //     }
  //     let htmlTemplate = reScheduleEmail(
  //       schedule?.name || "There",
  //       req?.therapist?.name || "The Therapist",
  //       recurrenceIdData.fromDate,
  //       recurrenceIdData.toDate,
  //       fromDate,
  //       toDate,
  //       googleCalenderEvent.link,
  //       schedule?.clientId?.defaultTimezone || "Asia/Kolkata",
  //       recurrenceIdData?.amount || 0
  //     );
  //     let senderData = {
  //       email: req.therapist.email,
  //       name: req.therapist.name,
  //     };
  //     const receiverData = [
  //       {
  //         email: schedule.clientId.email,
  //         name: schedule?.clientId.name,
  //       },
  //     ];
  //     let subject = MailSubjectEnum.APPOINTMENT_RESCHEDULE;
  //     const sentEmail = await Mailer2.sendMail(
  //       senderData,
  //       receiverData,
  //       subject,
  //       htmlTemplate
  //     );
  //     if (!sentEmail) {
  //       return res.status(400).json({ success: false, message: "Failed" });
  //     }
  //     res.send({ appointment: updatedSchedule });
  //   } catch (error) {
  //     next(error);
  //   }
  // }

  // static async cancelledSession(
  //   req: express.Request,
  //   res: express.Response,
  //   next: express.NextFunction
  // ) {
  //   try {
  //     const therapistId = req.therapist._id;

  //     // Extracting startDate and endDate from query parameters
  //     const startDate: any = req.query?.startDate;
  //     const endDate: any = req.query?.endDate;

  //     // Initialize arrays to hold cancelled session details
  //     const cancelledSessions: Array<{ name: string; session: number }> = [];

  //     // Create a date filter for MongoDB
  //     const dateFilter: any = {};

  //     if (startDate) {
  //       dateFilter["$gte"] = new Date(startDate);
  //     }
  //     if (endDate) {
  //       dateFilter["$lte"] = new Date(endDate);
  //     }

  //     // Fetch all cancelled sessions, applying optional date filters if provided
  //     const cancelledSessionRecords = await ScheduleModel.find({
  //       therapistId: String(therapistId),
  //       "recurrenceDates.status": "cancelled",
  //       ...(Object.keys(dateFilter).length
  //         ? { "recurrenceDates.fromDate": dateFilter }
  //         : {}),
  //     });

  //     cancelledSessionRecords.forEach((session) => {
  //       const sessionData = {
  //         name: session.name,
  //         session: session.recurrenceDates.filter(
  //           (date) => date.status === "cancelled"
  //         ).length,
  //       };
  //       cancelledSessions.push(sessionData);
  //     });

  //     const totalCancelledSessions = cancelledSessions.reduce(
  //       (total, session) => total + session.session,
  //       0
  //     );

  //     // Calculate collected fees from refunded sessions
  //     const totalCancelled = await ScheduleModel.find({
  //       therapistId: String(therapistId),
  //       "recurrenceDates.status": "cancelled",
  //       ...(Object.keys(dateFilter).length
  //         ? { "recurrenceDates.fromDate": dateFilter }
  //         : {}),
  //     });

  //     console.log(totalCancelled.length);

  //     const totalCancelledFees = totalCancelled.reduce((acc, curr) => {
  //       const sessionAmount = curr.recurrenceDates.reduce(
  //         (innerAcc: number, dateObj: { amount: number }) =>
  //           innerAcc + dateObj.amount,
  //         0
  //       );
  //       return acc + sessionAmount;
  //     }, 0);

  //     const completedSessions: any = await ScheduleModel.find({
  //       therapistId: String(therapistId),
  //       "recurrenceDates.status": "cancelled",
  //       ...(Object.keys(dateFilter).length
  //         ? { "recurrenceDates.fromDate": dateFilter }
  //         : {}),
  //     }).populate({
  //       path: "recurrenceDates.payTrackerId",
  //       select: "status amount",
  //     });

  //     // Initialize totals for pending and paid amounts
  //     let totalCollectedAmount = 0;
  //     let pendingFeesTotal = 0;

  //     // Iterate through each session
  //     completedSessions.forEach((session: any) => {
  //       session.recurrenceDates.forEach((recurrence: any) => {
  //         // Check if payTracker exists
  //         const payTracker = recurrence?.payTrackerId;

  //         if (payTracker) {
  //           if (payTracker.status === "Paid Cancellation") {
  //             totalCollectedAmount += payTracker.amount.value || 0; // Add amount if it exists
  //           }
  //         }
  //       });
  //     });

  //     pendingFeesTotal = totalCancelledFees - totalCollectedAmount;

  //     // console.log(totalCollectedAmount);
  //     // console.log(totalCancelledFees);
  //     // console.log(totalCancelledFees - totalCollectedAmount);

  //     // const collectedFees = await ScheduleModel.find({
  //     //   therapistId: String(therapistId),
  //     //   "recurrenceDates.status": "cancelled",
  //     //   "recurrenceDates.other.isRefunded": true,
  //     //   ...(Object.keys(dateFilter).length
  //     //     ? { "recurrenceDates.fromDate": dateFilter }
  //     //     : {}),
  //     // });

  //     // const collectedFeesTotal = collectedFees.reduce((acc, curr) => {
  //     //   const sessionAmount = curr.recurrenceDates.reduce(
  //     //     (innerAcc: number, dateObj: { amount: number }) =>
  //     //       innerAcc + dateObj.amount,
  //     //     0
  //     //   );
  //     //   return acc + sessionAmount;
  //     // }, 0);

  //     // // Calculate pending fees from non-refunded cancelled sessions
  //     // const pendingFees = await ScheduleModel.find({
  //     //   therapistId: String(therapistId),
  //     //   "recurrenceDates.status": "cancelled",
  //     //   "recurrenceDates.other.isRefunded": false,
  //     //   ...(Object.keys(dateFilter).length
  //     //     ? { "recurrenceDates.fromDate": dateFilter }
  //     //     : {}),
  //     // });

  //     // const pendingFeesTotal = pendingFees.reduce((acc, curr) => {
  //     //   const sessionAmount = curr.recurrenceDates.reduce(
  //     //     (innerAcc: number, dateObj: { amount: number }) =>
  //     //       innerAcc + dateObj.amount,
  //     //     0
  //     //   );
  //     //   return acc + sessionAmount;
  //     // }, 0);

  //     // Return the response
  //     res.json({
  //       cancelled_sessions: cancelledSessions,
  //       total_cancelled_sessions: totalCancelledSessions,
  //       collected_fees: totalCollectedAmount,
  //       pending_fees: pendingFeesTotal,
  //     });
  //   } catch (error) {
  //     next(error);
  //   }
  // }

  // static async cancelledSession(
  //   req: express.Request,
  //   res: express.Response,
  //   next: express.NextFunction
  // ) {
  //   try {
  //     const therapistId = req.therapist._id;

  //     // Extracting startDate and endDate from query parameters
  //     const startDate: any = req.query?.startDate;
  //     const endDate: any = req.query?.endDate;

  //     // Create a date filter for MongoDB
  //     const dateFilter: any = {};

  //     if (startDate) {
  //       dateFilter["$gte"] = new Date(startDate);
  //     }
  //     if (endDate) {
  //       dateFilter["$lte"] = new Date(endDate);
  //     }

  //     // Fetch all cancelled sessions, applying optional date filters if provided
  //     // const cancelledSessionRecords = await ScheduleModel.find({
  //     //   therapistId: String(therapistId),
  //     //   recurrenceDates: {
  //     //     $elemMatch: {
  //     //       status: "cancelled",
  //     //       ...(Object.keys(dateFilter).length ? { fromDate: dateFilter } : {}),
  //     //     },
  //     //   },
  //     // });

  //     // Fetch documents where at least one recurrenceDates item has status "cancelled"
  //     const allRecords = await ScheduleModel.find({
  //       therapistId: String(therapistId),
  //       "recurrenceDates.status": "cancelled",
  //       ...(Object.keys(dateFilter).length
  //         ? { "recurrenceDates.fromDate": dateFilter }
  //         : {}),
  //     });
  //     let totalCancelledCount = 0;

  //     const cancelledSessionRecords = allRecords.map((record) => {
  //       // Filter only the cancelled recurrenceDates
  //       const cancelledDates = record.recurrenceDates.filter((date) => {
  //         const isCancelled = date.status === "cancelled";

  //         // Check if dateFilter is empty or if fromDate is within the specified range
  //         const isInRange =
  //           Object.keys(dateFilter).length === 0 ||
  //           (date.fromDate >= dateFilter.startDate &&
  //             date.fromDate <= dateFilter.endDate);

  //         return isCancelled && isInRange;
  //       });

  //       // Accumulate the count of cancelled sessions across all documents
  //       totalCancelledCount += cancelledDates.length;

  //       return {
  //         ...record.toObject(), // Convert Mongoose document to plain JavaScript object
  //         recurrenceDates: cancelledDates, // Only cancelled recurrenceDates
  //         cancelledCount: cancelledDates.length, // Count of cancelled sessions in this document
  //       };
  //     });

  //     console.log(cancelledSessionRecords.length);

  //     // Aggregate cancelled sessions by name
  //     const sessionCountMap: { [name: string]: number } = {};

  //     cancelledSessionRecords.forEach((session) => {
  //       const cancelledCount = session.recurrenceDates.filter(
  //         (date: any) => date.status === "cancelled"
  //       ).length;

  //       // Accumulate the cancelled session count per name
  //       sessionCountMap[session.name] =
  //         (sessionCountMap[session.name] || 0) + cancelledCount;
  //     });

  //     // Convert aggregated data to the required format
  //     const cancelledSessions = Object.entries(sessionCountMap).map(
  //       ([name, count]) => ({
  //         name,
  //         session: count,
  //       })
  //     );

  //     // Calculate the total number of cancelled sessions
  //     // const totalCancelledSessions = cancelledSessions.reduce(
  //     //   (total, session) => total + session.session,
  //     //   0
  //     // );

  //     // Calculate collected fees from refunded sessions
  //     // const totalCancelledFees = cancelledSessionRecords.reduce((acc, curr) => {
  //     //   const sessionAmount = curr.recurrenceDates.reduce(
  //     //     (innerAcc: number, dateObj: { amount?: number; status: string }) =>
  //     //       dateObj.status === "cancelled"
  //     //         ? innerAcc + (dateObj.amount || 0)
  //     //         : innerAcc,
  //     //     0
  //     //   );
  //     //   return acc + sessionAmount;
  //     // }, 0);

  //     // // Find sessions with populated payTracker data for calculating collected and pending fees
  //     // const completedSessions: any = await ScheduleModel.find({
  //     //   therapistId: String(therapistId),
  //     //   "recurrenceDates.status": "cancelled",
  //     //   ...(Object.keys(dateFilter).length
  //     //     ? { "recurrenceDates.fromDate": dateFilter }
  //     //     : {}),
  //     // }).populate({
  //     //   path: "recurrenceDates.payTrackerId",
  //     //   select: "status amount",
  //     // });

  //     // // Initialize totals for pending and paid amounts
  //     // let totalCollectedAmount = 0;

  //     // // Calculate collected amount for paid cancellations
  //     // completedSessions.forEach((session: any) => {
  //     //   session.recurrenceDates.forEach((recurrence: any) => {
  //     //     const payTracker = recurrence?.payTrackerId;
  //     //     if (payTracker && payTracker.status === "Paid Cancellation") {
  //     //       totalCollectedAmount += payTracker.amount.value || 0;
  //     //     }
  //     //   });
  //     // });

  //     // // Calculate pending fees as the difference between total cancelled fees and collected amount
  //     // const pendingFeesTotal = totalCancelledFees - totalCollectedAmount;

  //     const totalCancelledFees = cancelledSessionRecords.reduce((acc, curr) => {
  //       const sessionAmount = curr.recurrenceDates.reduce(
  //         (innerAcc, dateObj) =>
  //           dateObj.status === "cancelled"
  //             ? innerAcc + (dateObj.amount || 0)
  //             : innerAcc,
  //         0
  //       );
  //       return acc + sessionAmount;
  //     }, 0);

  //     // Populate payTrackerId for each cancelled recurrence to calculate collected amount
  //     const completedSessions = await ScheduleModel.find({
  //       therapistId: String(therapistId),
  //       "recurrenceDates.status": "cancelled",
  //       ...(Object.keys(dateFilter).length
  //         ? { "recurrenceDates.fromDate": dateFilter }
  //         : {}),
  //     }).populate({
  //       path: "recurrenceDates.payTrackerId",
  //       select: "status amount",
  //     });

  //     // Initialize totals for pending and paid amounts
  //     let totalCollectedAmount = 0;

  //     // Calculate collected amount for paid cancellations
  //     completedSessions.forEach((session: any) => {
  //       session.recurrenceDates.forEach((recurrence: any) => {
  //         const payTracker = recurrence?.payTrackerId;
  //         if (payTracker && payTracker?.status === "Paid Cancellation") {
  //           totalCollectedAmount += payTracker?.amount.value || 0;
  //         }
  //       });
  //     });

  //     // Calculate pending fees as the difference between total cancelled fees and collected amount
  //     const pendingFeesTotal = totalCancelledFees - totalCollectedAmount;

  //     // Return the response with aggregated data
  //     res.json({
  //       cancelled_sessions: cancelledSessionRecords,
  //       total_cancelled_sessions: totalCancelledCount,
  //       collected_fees: totalCollectedAmount,
  //       pending_fees: pendingFeesTotal,
  //     });
  //   } catch (error) {
  //     next(error);
  //   }
  // }

  static async cancelledSession(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      // Extracting startDate and endDate from query parameters
      const startDate: any = req.query?.startDate;
      const endDate: any = req.query?.endDate;

      // Create a date filter for MongoDB query on recurrenceDates
      const dateFilter: any = {};
      if (startDate) dateFilter["$gte"] = new Date(startDate);
      if (endDate) dateFilter["$lte"] = new Date(endDate);

      // Fetch documents where recurrenceDates has "cancelled" status and apply date filter
      const allRecords = await ScheduleModel.find({
        therapistId: String(therapistId),
        "recurrenceDates.status": "cancelled",
        ...(Object.keys(dateFilter).length
          ? { "recurrenceDates.fromDate": dateFilter }
          : {}),
      }).populate({
        path: "recurrenceDates.payTrackerId",
        select: "status amount cancellationFee",
      });

      let totalCancelledCount = 0;
      let totalCollectedAmount = 0;
      let totalAmount = 0;

      // Filter cancelled recurrenceDates within range
      const cancelledSessionRecords = allRecords.map((record) => {
        const cancelledDates = record.recurrenceDates.filter((date) => {
          const isCancelled = date.status === "cancelled";
          const isInRange =
            (!startDate || new Date(date.fromDate) >= new Date(startDate)) &&
            (!endDate || new Date(date.fromDate) <= new Date(endDate));

          return isCancelled && isInRange;
        });

        // Calculate collected and pending fees based on payTrackerId status
        cancelledDates.forEach((date: any) => {
          totalAmount += date.payTrackerId.amount.value || 0;
          if (date.payTrackerId?.status === "Paid Cancellation") {
            totalCollectedAmount +=
              date.payTrackerId.cancellationFee.value || 0;
          }
        });

        totalCancelledCount += cancelledDates.length;

        return {
          ...record.toObject(),
          recurrenceDates: cancelledDates,
          cancelledCount: cancelledDates.length,
        };
      });

      // Aggregate cancelled sessions by name, excluding zero-count sessions
      const sessionCountMap: { [name: string]: number } = {};
      cancelledSessionRecords.forEach((session) => {
        if (session.cancelledCount > 0) {
          sessionCountMap[session.name] =
            (sessionCountMap[session.name] || 0) + session.cancelledCount;
        }
      });

      // Format the aggregated data, excluding sessions with zero cancellations
      const cancelledSessions = Object.entries(sessionCountMap)
        .map(([name, count]) => ({
          name,
          session: count,
        }))
        .filter((session) => session.session > 0);

      const pendingFeesTotal = totalAmount - totalCollectedAmount;

      // Return response with aggregated data
      res.json({
        cancelled_sessions: cancelledSessions,
        total_cancelled_sessions: totalCancelledCount,
        collected_fees: totalCollectedAmount,
        pending_fees: pendingFeesTotal,
      });
    } catch (error) {
      next(error);
    }
  }

  static async getClients(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const pageNumber: number = Number(req.query.pageNumber) || 1;
      const pageSize: number = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const searchText = req.query.searchText || "";
      const startDate: Date | undefined = req.query.startDate
        ? new Date(req.query.startDate as string)
        : undefined;
      const endDate: Date | undefined = req.query.endDate
        ? new Date(req.query.endDate as string)
        : undefined;
      const isActive: boolean | undefined =
        req.query.isActive === "true"
          ? true
          : req.query.isActive === "false"
          ? false
          : undefined;
      const fromPublicCalender: boolean | undefined =
        req.query.fromPublicCalender === "true"
          ? true
          : req.query.fromPublicCalender === "false"
          ? false
          : undefined;

      let count = 0;

      const totalClients = await ClientDao.getAllClientsByTherapist(
        therapistId,
        searchText,
        startDate,
        endDate,
        isActive,
        fromPublicCalender
      );

      let paginationClients = await ClientService.getAllClient(
        therapistId,
        skip,
        pageSize,
        searchText,
        startDate,
        endDate,
        isActive,
        fromPublicCalender
      );

      count = totalClients.length;

      // Step 2: Fetch session and last session details for each client
      // const clientsWithDetails = await Promise.all(
      //   paginationClients.map(async (client: any) => {
      //     // Initialize last_session and session stats for each client
      //     let last_session: { date: any; minutes: number } = {
      //       date: null,
      //       minutes: 0,
      //     };
      //     let session = {
      //       pending_session: 0,
      //       completed_session: 0,
      //       total_session: 0,
      //       pending_total: 0,
      //       completed_total: 0,
      //       total: 0,
      //     };

      //     const filter: { [key: string]: any } = {
      //       therapistId: String(therapistId),
      //       clientId: String(client._id),
      //     };

      //     const projection = { recurrenceDates: 1, _id: 1 };
      //     const options = {
      //       limit: 1,
      //       sort: { "recurrenceDates.fromDate": -1 },
      //     };

      //     const schedule = await ScheduleModel.find(
      //       filter,
      //       projection,
      //       options
      //     );
      //     if (schedule.length > 0) {
      //       schedule[0].recurrenceDates.forEach(
      //         ({ fromDate, toDate }: { fromDate: Date; toDate: Date }) => {
      //           const differenceInMinutes = Math.floor(
      //             (toDate.getTime() - fromDate.getTime()) / (1000 * 60)
      //           );

      //           last_session.date = fromDate;
      //           last_session.minutes = differenceInMinutes;
      //         }
      //       );
      //     }

      //     const pendingSession = await ScheduleModel.find({
      //       therapistId: String(therapistId),
      //       clientId: String(client._id),
      //       "recurrenceDates.status": "pending",
      //     });

      //     const completedSession = await ScheduleModel.find({
      //       therapistId: String(therapistId),
      //       clientId: String(client._id),
      //       "recurrenceDates.status": "completed",
      //     });

      //     const totalSession = pendingSession.length + completedSession.length;

      //     session.pending_session = pendingSession.length;
      //     session.completed_session = completedSession.length;
      //     session.total_session = totalSession;

      //     const completedSessions: any = await ScheduleModel.find({
      //       therapistId: String(therapistId),
      //       clientId: String(client._id),
      //     }).populate({
      //       path: "recurrenceDates.payTrackerId",
      //       select: "status amount",
      //     });

      //     // Initialize totals for pending and paid amounts
      //     let totalPendingAmount = 0;
      //     let totalCollectedAmount = 0;

      //     // Iterate through each session
      //     completedSessions.forEach((session: any) => {
      //       session.recurrenceDates.forEach((recurrence: any) => {
      //         // Check if payTracker exists
      //         const payTracker = recurrence?.payTrackerId;

      //         // console.log(payTracker);
      //         if (payTracker) {
      //           if (payTracker.status === "Still pending") {
      //             totalPendingAmount += payTracker.amount.value || 0; // Add amount if it exists
      //           } else if (
      //             payTracker.status === "Paid on time" ||
      //             payTracker.status === "Paid delayed"
      //           ) {
      //             totalCollectedAmount += payTracker.amount.value || 0;
      //           }
      //         }
      //       });
      //     });

      //     const total = totalPendingAmount + totalCollectedAmount;

      //     // // Log the totals
      //     // console.log(`Total Pending Amount: ${totalPendingAmount}`);
      //     // console.log(`Total Paid Amount: ${totalCollectedAmount}`);
      //     // console.log(`Total Amount: ${total}`);

      //     session.pending_total = totalPendingAmount;
      //     session.completed_total = totalCollectedAmount;
      //     session.total = total;

      //     // Add last_session and session to the client object
      //     return {
      //       ...client,
      //       last_session,
      //       session,
      //     };
      //   })
      // );

      const clientsWithDetails = await Promise.all(
        paginationClients.map(async (client: any) => {
          // Initialize last_session and session stats for each client
          let last_session: { date: any; minutes: number } = {
            date: null,
            minutes: 0,
          };
          let session = {
            pending_session: 0,
            completed_session: 0,
            total_session: 0,
            pending_total: 0,
            completed_total: 0,
            total: 0,
          };

          const filter: { [key: string]: any } = {
            therapistId: String(therapistId),
            clientId: String(client._id),
          };

          const projection = { recurrenceDates: 1, _id: 1 };

          const schedules = await ScheduleModel.find(filter, projection);

          schedules.forEach((schedule) => {
            schedule.recurrenceDates.forEach(
              ({ fromDate, toDate }: { fromDate: Date; toDate: Date }) => {
                const differenceInMinutes = Math.floor(
                  (toDate.getTime() - fromDate.getTime()) / (1000 * 60)
                );

                // Update last_session if this session is more recent
                if (!last_session.date || fromDate > last_session.date) {
                  last_session.date = fromDate;
                  last_session.minutes = differenceInMinutes;
                }
              }
            );
          });

          // const pendingSession = await ScheduleModel.find({
          //   therapistId: String(therapistId),
          //   clientId: String(client._id),
          //   "recurrenceDates.status": "pending",
          // });

          // const pendingSession = await ScheduleModel.find({
          //   therapistId: String(therapistId),
          //   clientId: String(client._id),
          //   "recurrenceDates.status": { $in: ["pending", "confirmed"] },
          // });

          // const completedSession = await ScheduleModel.find({
          //   therapistId: String(therapistId),
          //   clientId: String(client._id),
          //   "recurrenceDates.status": "completed",
          // });

          // const totalSession = pendingSession.length + completedSession.length;

          // session.pending_session = pendingSession.length;
          // session.completed_session = completedSession.length;
          // session.total_session = totalSession;

          // Find all schedules with pending, confirmed, or rescheduled sessions
          const pendingSessionDocuments = await ScheduleModel.find({
            therapistId: String(therapistId),
            clientId: String(client._id),
            "recurrenceDates.status": {
              $in: ["pending", "confirmed", "rescheduled"],
            },
          });

          // Count the number of pending, confirmed, or rescheduled sessions
          let pendingSessionCount = 0;
          pendingSessionDocuments.forEach((doc) => {
            pendingSessionCount += doc.recurrenceDates.filter((recurrence) =>
              ["pending", "confirmed", "rescheduled"].includes(
                recurrence.status
              )
            ).length;
          });

          // Find all schedules with completed sessions
          const completedSessionDocuments = await ScheduleModel.find({
            therapistId: String(therapistId),
            clientId: String(client._id),
            "recurrenceDates.status": "completed",
          });

          // Count the number of completed sessions
          let completedSessionCount = 0;
          completedSessionDocuments.forEach((doc) => {
            completedSessionCount += doc.recurrenceDates.filter(
              (recurrence) => recurrence.status === "completed"
            ).length;
          });

          // console.log("Number of pending sessions:", pendingSessionCount);
          // console.log("Number of completed sessions:", completedSessionCount);

          const totalSession = pendingSessionCount + completedSessionCount;

          session.pending_session = pendingSessionCount;
          session.completed_session = completedSessionCount;
          session.total_session = totalSession;

          const completedSessions: any = await ScheduleModel.find({
            therapistId: String(therapistId),
            clientId: String(client._id),
          }).populate({
            path: "recurrenceDates.payTrackerId",
            select: "status amount",
          });

          // Initialize totals for pending and paid amounts
          let totalPendingAmount = 0;
          let totalCollectedAmount = 0;

          // Iterate through each session
          completedSessions.forEach((session: any) => {
            session.recurrenceDates.forEach((recurrence: any) => {
              // Check if payTracker exists
              const payTracker = recurrence?.payTrackerId;

              if (payTracker) {
                if (payTracker.status === "Still pending") {
                  totalPendingAmount += payTracker.amount.value || 0; // Add amount if it exists
                } else if (
                  payTracker.status === "Paid on time" ||
                  payTracker.status === "Paid Delay"
                ) {
                  totalCollectedAmount += payTracker.amount.value || 0;
                }
              }
            });
          });

          const total = totalPendingAmount + totalCollectedAmount;

          session.pending_total = totalPendingAmount;
          session.completed_total = totalCollectedAmount;
          session.total = total;

          // Add last_session and session to the client object
          return {
            ...client,
            last_session,
            session,
          };
        })
      );

      // Step 3: Send the response with paginated clients and their session details
      res.send({
        clients: clientsWithDetails,
        totalClients: count,
        pageNumber,
        pageSize,
      });
    } catch (error) {
      next(error);
    }
  }

  static async regularClient(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      let count = 0;

      const totalClients = await ClientDao.getAllClientsByTherapist(
        therapistId
      );

      let paginationClients = await ClientDao.getAllRegularClient(therapistId);

      count = totalClients.length;

      const clientsWithDetails = await Promise.all(
        paginationClients.map(async (client: any) => {
          // Initialize last_session and session stats for each client
          let last_session: { date: any; minutes: number } = {
            date: null,
            minutes: 0,
          };
          let session = {
            pending_session: 0,
            completed_session: 0,
            total_session: 0,
            pending_total: 0,
            completed_total: 0,
            total: 0,
          };

          const filter: { [key: string]: any } = {
            therapistId: String(therapistId),
            clientId: String(client._id),
          };

          const projection = { recurrenceDates: 1, _id: 1 };

          const schedules = await ScheduleModel.find(filter, projection);

          schedules.forEach((schedule) => {
            schedule.recurrenceDates.forEach(
              ({ fromDate, toDate }: { fromDate: Date; toDate: Date }) => {
                const differenceInMinutes = Math.floor(
                  (toDate.getTime() - fromDate.getTime()) / (1000 * 60)
                );

                // Update last_session if this session is more recent
                if (!last_session.date || fromDate > last_session.date) {
                  last_session.date = fromDate;
                  last_session.minutes = differenceInMinutes;
                }
              }
            );
          });

          // const pendingSession = await ScheduleModel.find({
          //   therapistId: String(therapistId),
          //   clientId: String(client._id),
          //   "recurrenceDates.status": {
          //     $in: ["pending", "confirmed", "rescheduled"],
          //   },
          // });

          // const completedSession = await ScheduleModel.find({
          //   therapistId: String(therapistId),
          //   clientId: String(client._id),
          //   "recurrenceDates.status": "completed",
          // });

          // Find all schedules with pending, confirmed, or rescheduled sessions
          const pendingSessionDocuments = await ScheduleModel.find({
            therapistId: String(therapistId),
            clientId: String(client._id),
            "recurrenceDates.status": {
              $in: ["pending", "confirmed", "rescheduled"],
            },
          });

          // Count the number of pending, confirmed, or rescheduled sessions
          let pendingSessionCount = 0;
          pendingSessionDocuments.forEach((doc) => {
            pendingSessionCount += doc.recurrenceDates.filter((recurrence) =>
              ["pending", "confirmed", "rescheduled"].includes(
                recurrence.status
              )
            ).length;
          });

          // Find all schedules with completed sessions
          const completedSessionDocuments = await ScheduleModel.find({
            therapistId: String(therapistId),
            clientId: String(client._id),
            "recurrenceDates.status": "completed",
          });

          // Count the number of completed sessions
          let completedSessionCount = 0;
          completedSessionDocuments.forEach((doc) => {
            completedSessionCount += doc.recurrenceDates.filter(
              (recurrence) => recurrence.status === "completed"
            ).length;
          });

          // console.log("Number of pending sessions:", pendingSessionCount);
          // console.log("Number of completed sessions:", completedSessionCount);

          const totalSession = pendingSessionCount + completedSessionCount;

          session.pending_session = pendingSessionCount;
          session.completed_session = completedSessionCount;
          session.total_session = totalSession;

          const completedSessions: any = await ScheduleModel.find({
            therapistId: String(therapistId),
            clientId: String(client._id),
          }).populate({
            path: "recurrenceDates.payTrackerId",
            select: "status amount",
          });

          // Initialize totals for pending and paid amounts
          let totalPendingAmount = 0;
          let totalCollectedAmount = 0;

          // Iterate through each session
          completedSessions.forEach((session: any) => {
            session.recurrenceDates.forEach((recurrence: any) => {
              // Check if payTracker exists
              const payTracker = recurrence?.payTrackerId;

              if (payTracker) {
                if (payTracker.status === "Still pending") {
                  totalPendingAmount += payTracker.amount.value || 0; // Add amount if it exists
                } else if (
                  payTracker.status === "Paid on time" ||
                  payTracker.status === "Paid Delay"
                ) {
                  totalCollectedAmount += payTracker.amount.value || 0;
                }
              }
            });
          });

          const total = totalPendingAmount + totalCollectedAmount;

          session.pending_total = totalPendingAmount;
          session.completed_total = totalCollectedAmount;
          session.total = total;

          // Add last_session and session to the client object
          return {
            ...client,
            last_session,
            session,
          };
        })
      );

      const sortedClients = clientsWithDetails.sort(
        (a, b) => b.session.total_session - a.session.total_session
      );

      // Get the top 5 clients with the most total_session
      const top5Clients = sortedClients.slice(0, 4);

      // Step 3: Send the response with paginated clients and their session details
      res.send({
        clients: top5Clients,
        totalClients: count,
      });
    } catch (error) {
      next(error);
    }
  }

  static async askForPaymentOnCancellation(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const recurrenceDateId = req.params.id;

      let paymentLink = false;
      let paymentLinkPaid = false;
      let askFromTherapist = false;

      const schedule = await ScheduleDao.getScheduleByRecurrenceDateId(
        therapistId,
        recurrenceDateId
      );
      if (!schedule)
        return res
          .status(400)
          .json({ success: false, message: "Unable to find schedule." });

      const recurrenceIdData = schedule.recurrenceDates.find(
        (recurrence: any) => String(recurrence._id) == String(recurrenceDateId)
      );
      if (!recurrenceIdData)
        return res
          .status(400)
          .json({ success: false, message: "Unable to find recurrence date." });

      const diff = moment(recurrenceIdData.fromDate).diff(moment(), "hours");
      const transactionDetails = await TransactionService.getTransactionByRecId(
        recurrenceIdData._id,
        therapistId
      );
      if (transactionDetails) {
        if (transactionDetails.paymentStatus === paymentStatus.COMPLETE) {
          paymentLink = true;
          paymentLinkPaid = true;
        }
        if (transactionDetails.paymentStatus === paymentStatus.PENDING) {
          paymentLink = true;
        }
      } else {
        return res.status(200).send({
          paymentLink: paymentLink,
          paymentLinkPaid: paymentLinkPaid,
          askFromTherapist: askFromTherapist,
        });
      }
      if (diff > 48) {
        return res.status(200).send({
          paymentLink: paymentLink,
          paymentLinkPaid: paymentLinkPaid,
          askFromTherapist: askFromTherapist,
        });
      } else {
        askFromTherapist = true;
        return res.status(200).send({
          paymentLink: paymentLink,
          paymentLinkPaid: paymentLinkPaid,
          askFromTherapist: askFromTherapist,
        });
      }
    } catch (e) {
      next(e);
    }
  }

  // static async cancelSchedule(
  //   req: express.Request,
  //   res: express.Response,
  //   next: express.NextFunction
  // ) {
  //   try {
  //     const therapistId = req.therapist._id;
  //     const recurrenceDateId = req.params.id;
  //     const therapistFine = req.body.amount || 0;
  //     let paymentLink = undefined;

  //     const schedule: any = await ScheduleDao.getScheduleByRecurrenceDateId(
  //       therapistId,
  //       recurrenceDateId
  //     );
  //     if (!schedule) {
  //       return res
  //         .status(404)
  //         .json({ success: false, message: "No schedule found." });
  //     }
  //     const recurrenceIdData: any = await schedule?.recurrenceDates.find(
  //       (recurrence: any) => String(recurrence._id) == String(recurrenceDateId)
  //     );
  //     if (therapistFine > recurrenceIdData.amount) {
  //       return res.status(400).json({
  //         success: false,
  //         message: "Therapist Fine should not exceed session amount.",
  //       });
  //     }
  //     const transactionDetails: any =
  //       await TransactionService.getTransactionByRecId(
  //         recurrenceDateId,
  //         req.therapist._id
  //       );
  //     if (!transactionDetails) {
  //       if (recurrenceIdData.calenderEventId) {
  //         const removeEvent =
  //           await GoogleCalendarService.removeEventFromCalendar(
  //             therapistId,
  //             recurrenceIdData
  //           );
  //       }
  //       let htmlTemplate = cancelScheduleEmail(
  //         schedule?.name,
  //         req.therapist.name,
  //         recurrenceIdData.fromDate,
  //         recurrenceIdData.toDate,
  //         undefined,
  //         schedule?.clientId?.defaultTimezone,
  //         paymentLink
  //       );
  //       let senderData = {
  //         email: req.therapist.email,
  //         name: req.therapist.name,
  //       };
  //       const receiverData = [
  //         {
  //           email: schedule.email,
  //           name: schedule?.name,
  //         },
  //       ];
  //       let subject = MailSubjectEnum.APPOINTMENT_CANCELLED;
  //       const sentEmail = await Mailer2.sendMail(
  //         senderData,
  //         receiverData,
  //         subject,
  //         htmlTemplate
  //       );
  //       if (!sentEmail) {
  //         return res.status(400).json({ success: false, message: "Failed" });
  //       }
  //       const updatedSchedule = await ScheduleDao.cancelSchedule(
  //         therapistId,
  //         recurrenceDateId
  //       );
  //       if (!updatedSchedule) {
  //         return res
  //           .status(400)
  //           .json({ success: false, message: "Unable to cancel appointment." });
  //       }
  //       return res.send("Appointment Cancelled.");
  //     }
  //     let transactionCompleted = false;
  //     let refundedAmount = 0;
  //     if (therapistFine == 0) {
  //       if (transactionDetails.paymentStatus === paymentStatus.COMPLETE) {
  //         transactionCompleted = true;
  //       }
  //       if (transactionDetails.paymentStatus === paymentStatus.PENDING) {
  //         const cancelPaymentLink = await RazorpayService.cancelPaymentLink(
  //           transactionDetails?.paymentSessionId
  //         );
  //         if (!cancelPaymentLink) {
  //           return res.status(400).json({
  //             success: false,
  //             message: "Unable to cancel payment link.",
  //           });
  //         }

  //         const updateTransaction = await TransactionService.cancelPayment(
  //           transactionDetails._id,
  //           therapistId
  //         );
  //         if (!updateTransaction) {
  //           return res.status(400).json({
  //             success: false,
  //             message: "Unable to update transaction.",
  //           });
  //         }
  //       }
  //       if (transactionCompleted) {
  //         // const sessionCancelledDeduction = await DeductionService.createRefundDeduction(therapistId, recurrenceIdData.amount, schedule._id, recurrenceIdData._id, schedule.clientId?._id);
  //         // if(!sessionCancelledDeduction){
  //         //     return res.status(500).send("Unable to create deduction.")
  //         // }
  //         let deduction = new deductionModel({
  //           therapistId: therapistId,
  //           amount: recurrenceIdData.amount,
  //           deductionType: DeductionTypeEnum.REFUNDTOCLIENT,
  //           scheduleId: schedule._id,
  //           scheduleRecId: recurrenceIdData._id,
  //           clientId: schedule.clientId?._id,
  //           deductionDate: new Date(),
  //           remarks: "Refund to Client - RZP",
  //         });
  //         const refund = await RazorpayService.refund(
  //           transactionDetails?.paymentDetails.payload.payment.entity.id,
  //           recurrenceIdData.amount,
  //           req.therapist.name,
  //           "Session Cancellation",
  //           deduction._id
  //         );
  //         if (!refund) {
  //           return res
  //             .status(400)
  //             .json({ success: false, message: "Unable to process refund." });
  //         }
  //         deduction.rzp_deductionId = refund.id;
  //         await deduction.save();
  //         refundedAmount = recurrenceIdData.amount;
  //       }
  //     }

  //     if (therapistFine > 0) {
  //       if (transactionDetails) {
  //         if (transactionDetails.paymentStatus === paymentStatus.COMPLETE) {
  //           transactionCompleted = true;
  //         }
  //         if (transactionDetails.paymentStatus === paymentStatus.PENDING) {
  //           const cancelPaymentLink = await RazorpayService.cancelPaymentLink(
  //             transactionDetails.paymentSessionId
  //           );
  //           if (!cancelPaymentLink) {
  //             return res.status(400).json({
  //               success: false,
  //               message: "Unable to cancel old payment link.",
  //             });
  //           }
  //           transactionDetails.paymentStatus = paymentStatus.CANCELLED;
  //           await transactionDetails.save();
  //           const isFine = true;
  //           const finePaymentLink = await RazorpayService.createPaymentLink(
  //             therapistFine,
  //             req.therapist._id,
  //             schedule._id,
  //             recurrenceDateId,
  //             schedule.clientId?._id,
  //             isFine
  //           );
  //           if (!finePaymentLink) {
  //             return res.status(400).json({
  //               success: false,
  //               message: "Unable to create fine payment link.",
  //             });
  //           }
  //           paymentLink = finePaymentLink.paymentLink;
  //         }
  //       }
  //       if (transactionCompleted) {
  //         const refundAmount =
  //           Number(recurrenceIdData.amount) - Number(therapistFine);
  //         refundedAmount = refundAmount;

  //         let deduction = new deductionModel({
  //           therapistId: therapistId,
  //           amount: refundAmount,
  //           deductionType: DeductionTypeEnum.REFUNDTOCLIENT,
  //           scheduleId: schedule._id,
  //           scheduleRecId: recurrenceIdData._id,
  //           clientId: schedule.clientId?._id,
  //           deductionDate: new Date(),
  //           remarks: "Refund to Client - RZP",
  //         });

  //         if (refundAmount > 0) {
  //           const refund = await RazorpayService.refund(
  //             transactionDetails?.paymentDetails.payload.payment.entity.id,
  //             refundAmount,
  //             req.therapist.name,
  //             "Session Cancellation",
  //             deduction._id
  //           );
  //           if (!refund) {
  //             return res
  //               .status(400)
  //               .json({ success: false, message: "Unable to process refund." });
  //           }
  //           deduction.rzp_deductionId = refund.id;
  //         }

  //         await deduction.save();
  //         // const sessionCancelledDeduction = await DeductionService.createRefundDeduction(therapistId, refundAmount, schedule._id, recurrenceIdData._id, schedule.clientId?._id);
  //         // if(!sessionCancelledDeduction){
  //         //     return res.status(500).send("Unable to create deduction.")
  //         // }
  //       }
  //     }
  //     if (recurrenceIdData.calenderEventId) {
  //       const removeEvent = await GoogleCalendarService.removeEventFromCalendar(
  //         therapistId,
  //         recurrenceIdData
  //       );
  //     }
  //     let htmlTemplate = cancelScheduleEmail(
  //       schedule?.name,
  //       req.therapist.name,
  //       recurrenceIdData.fromDate,
  //       recurrenceIdData.toDate,
  //       refundedAmount,
  //       schedule?.clientId?.defaultTimezone,
  //       paymentLink
  //     );
  //     let senderData = {
  //       email: req.therapist.email,
  //       name: req.therapist.name,
  //     };
  //     const receiverData = [
  //       {
  //         email: schedule.email,
  //         name: schedule?.name,
  //       },
  //     ];
  //     let subject = MailSubjectEnum.APPOINTMENT_CANCELLED;
  //     const sentEmail = await Mailer2.sendMail(
  //       senderData,
  //       receiverData,
  //       subject,
  //       htmlTemplate
  //     );
  //     if (!sentEmail) {
  //       return res.status(400).json({ success: false, message: "Failed" });
  //     }
  //     const updatedSchedule = await ScheduleDao.cancelSchedule(
  //       therapistId,
  //       recurrenceDateId
  //     );
  //     if (!updatedSchedule) {
  //       return res
  //         .status(400)
  //         .json({ success: false, message: "Unable to cancel appointment." });
  //     }
  //     res.send("Appointment Cancelled.");
  //   } catch (error) {
  //     next(error);
  //   }
  // }

  static async cancelSchedule(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const recurrenceDateId = req.params.id;
      const therapistFine = req.body.amount || 0;
      const cancelled_session = req.body.cancelled_session;
      let paymentLink = undefined;

      if (!cancelled_session) {
        return res.status(400).json({
          success: false,
          message: "cancelled_session is required",
        });
      }

      const schedule: any = await ScheduleDao.getScheduleByRecurrenceDateId(
        therapistId,
        recurrenceDateId
      );
      if (!schedule) {
        return res
          .status(404)
          .json({ success: false, message: "No schedule found." });
      }
      const recurrenceIdData: any = await schedule?.recurrenceDates.find(
        (recurrence: any) => String(recurrence._id) == String(recurrenceDateId)
      );
      if (therapistFine > recurrenceIdData.amount) {
        return res.status(400).json({
          success: false,
          message: "Therapist Fine should not exceed session amount.",
        });
      }
      const transactionDetails: any =
        await TransactionService.getTransactionByRecId(
          recurrenceDateId,
          req.therapist._id
        );
      if (!transactionDetails) {
        if (recurrenceIdData.calenderEventId) {
          // Handle calendar event deletion based on cancelled_session type
          if (cancelled_session === "this session only") {
            await GoogleCalendarService.removeEventFromCalendar(
              therapistId,
              recurrenceIdData,
              false // deleteEntireSeries = false
            );
          } else if (cancelled_session === "cancel entire slot") {
            await GoogleCalendarService.removeEventFromCalendar(
              therapistId,
              recurrenceIdData,
              true // deleteEntireSeries = true
            );
          }
        }
        let htmlTemplate = cancelScheduleEmail(
          schedule?.name || "There",
          req.therapist.name,
          recurrenceIdData.fromDate,
          recurrenceIdData.toDate,
          undefined,
          schedule?.clientId?.defaultTimezone,
          paymentLink
        );
        let senderData = {
          email: req.therapist.email,
          name: req.therapist.name,
        };
        const receiverData = [
          {
            email: schedule.email,
            name: schedule?.name || "There",
          },
        ];
        let subject = MailSubjectEnum.APPOINTMENT_CANCELLED;
        const sentEmail = await Mailer2.sendMail(
          senderData,
          receiverData,
          subject,
          htmlTemplate
        );

        const formattedDate = format(
          new Date(recurrenceIdData.fromDate),
          "dd-MM-yyyy"
        );
        const formattedTime = format(
          new Date(recurrenceIdData.fromDate),
          "HH:mm"
        );

        const params = {
          CLIENT_NAME: schedule?.clientId.name,
          THERAPIST_NAME: req?.therapist?.name,
          SESSION_DATE: formattedDate,
          SESSION_TIME: formattedTime,
        };

        await sendWhatsAppNotification(
          schedule?.clientId,
          "free_cancellation_session3",
          params
        );

        if (!sentEmail) {
          return res.status(400).json({ success: false, message: "Failed" });
        }
        let updatedSchedule;
        if (cancelled_session === "this session only") {
          updatedSchedule = await ScheduleDao.cancelSchedule(
            therapistId,
            recurrenceDateId
          );
        } else if (cancelled_session === "cancel entire slot") {
          updatedSchedule = await ScheduleDao.cancelAllSchedule(
            therapistId,
            schedule.clientId._id,
            recurrenceDateId
          );

          if (updatedSchedule) {
            await TherapistController.removeCalendarEvents(
              therapistId,
              schedule.recurrenceDates
            );
          }
        }
        if (!updatedSchedule) {
          return res.status(400).json({
            success: false,
            message: "Unable to cancel appointment.",
          });
        }

        return res.send("Appointment Cancelled.");
      }
      let transactionCompleted = false;
      let refundedAmount = 0;
      if (therapistFine == 0) {
        if (transactionDetails.paymentStatus === paymentStatus.COMPLETE) {
          transactionCompleted = true;
        }
        if (transactionDetails.paymentStatus === paymentStatus.PENDING) {
          const cancelPaymentLink = await RazorpayService.cancelPaymentLink(
            transactionDetails?.paymentSessionId
          );
          if (!cancelPaymentLink) {
            return res.status(400).json({
              success: false,
              message: "Unable to cancel payment link.",
            });
          }

          const updateTransaction = await TransactionService.cancelPayment(
            transactionDetails._id,
            therapistId
          );
          if (!updateTransaction) {
            return res.status(400).json({
              success: false,
              message: "Unable to update transaction.",
            });
          }
        }
        if (transactionCompleted) {
          // const sessionCancelledDeduction = await DeductionService.createRefundDeduction(therapistId, recurrenceIdData.amount, schedule._id, recurrenceIdData._id, schedule.clientId?._id);
          // if(!sessionCancelledDeduction){
          //     return res.status(500).send("Unable to create deduction.")
          // }
          let deduction = new deductionModel({
            therapistId: therapistId,
            amount: recurrenceIdData.amount,
            deductionType: DeductionTypeEnum.REFUNDTOCLIENT,
            scheduleId: schedule._id,
            scheduleRecId: recurrenceIdData._id,
            clientId: schedule.clientId?._id,
            deductionDate: new Date(),
            remarks: "Refund to Client - RZP",
          });
          const refund = await RazorpayService.refund(
            transactionDetails?.paymentDetails.payload.payment.entity.id,
            recurrenceIdData.amount,
            req.therapist.name,
            "Session Cancellation",
            deduction._id
          );
          if (!refund) {
            return res
              .status(400)
              .json({ success: false, message: "Unable to process refund." });
          }
          deduction.rzp_deductionId = refund.id;
          await deduction.save();
          refundedAmount = recurrenceIdData.amount;
        }
      }

      if (therapistFine > 0) {
        if (transactionDetails) {
          if (transactionDetails.paymentStatus === paymentStatus.COMPLETE) {
            transactionCompleted = true;
          }
          if (transactionDetails.paymentStatus === paymentStatus.PENDING) {
            const cancelPaymentLink = await RazorpayService.cancelPaymentLink(
              transactionDetails.paymentSessionId
            );
            if (!cancelPaymentLink) {
              return res.status(400).json({
                success: false,
                message: "Unable to cancel old payment link.",
              });
            }
            transactionDetails.paymentStatus = paymentStatus.CANCELLED;
            await transactionDetails.save();
            const isFine = true;
            const finePaymentLink = await RazorpayService.createPaymentLink(
              therapistFine,
              req.therapist._id,
              schedule._id,
              recurrenceDateId,
              schedule.clientId?._id,
              isFine
            );
            if (!finePaymentLink) {
              return res.status(400).json({
                success: false,
                message: "Unable to create fine payment link.",
              });
            }
            paymentLink = finePaymentLink.paymentLink;
          }
        }
        if (transactionCompleted) {
          const refundAmount =
            Number(recurrenceIdData.amount) - Number(therapistFine);
          refundedAmount = refundAmount;

          let deduction = new deductionModel({
            therapistId: therapistId,
            amount: refundAmount,
            deductionType: DeductionTypeEnum.REFUNDTOCLIENT,
            scheduleId: schedule._id,
            scheduleRecId: recurrenceIdData._id,
            clientId: schedule.clientId?._id,
            deductionDate: new Date(),
            remarks: "Refund to Client - RZP",
          });

          if (refundAmount > 0) {
            const refund = await RazorpayService.refund(
              transactionDetails?.paymentDetails.payload.payment.entity.id,
              refundAmount,
              req.therapist.name,
              "Session Cancellation",
              deduction._id
            );
            if (!refund) {
              return res
                .status(400)
                .json({ success: false, message: "Unable to process refund." });
            }
            deduction.rzp_deductionId = refund.id;
          }

          await deduction.save();
          // const sessionCancelledDeduction = await DeductionService.createRefundDeduction(therapistId, refundAmount, schedule._id, recurrenceIdData._id, schedule.clientId?._id);
          // if(!sessionCancelledDeduction){
          //     return res.status(500).send("Unable to create deduction.")
          // }
        }
      }
      if (recurrenceIdData.calenderEventId) {
        if (cancelled_session === "this session only") {
          await GoogleCalendarService.removeEventFromCalendar(
            therapistId,
            recurrenceIdData,
            false
          );
        } else if (cancelled_session === "cancel entire slot") {
          await GoogleCalendarService.removeEventFromCalendar(
            therapistId,
            recurrenceIdData,
            true
          );
        }
      }
      let htmlTemplate = cancelScheduleEmail(
        schedule?.name || "There",
        req.therapist.name,
        recurrenceIdData.fromDate,
        recurrenceIdData.toDate,
        refundedAmount,
        schedule?.clientId?.defaultTimezone,
        paymentLink
      );
      let senderData = {
        email: req.therapist.email,
        name: req.therapist.name,
      };
      const receiverData = [
        {
          email: schedule.email,
          name: schedule?.name || "There",
        },
      ];
      let subject = MailSubjectEnum.APPOINTMENT_CANCELLED;
      const sentEmail = await Mailer2.sendMail(
        senderData,
        receiverData,
        subject,
        htmlTemplate
      );
      if (!sentEmail) {
        return res.status(400).json({ success: false, message: "Failed" });
      }

      const formattedDate = format(
        new Date(recurrenceIdData.fromDate),
        "dd-MM-yyyy"
      );
      const formattedTime = format(
        new Date(recurrenceIdData.fromDate),
        "HH:mm"
      );

      const params = {
        CLIENT_NAME: schedule?.clientId.name,
        THERAPIST_NAME: req?.therapist?.name,
        SESSION_DATE: formattedDate,
        SESSION_TIME: formattedTime,
      };

      await sendWhatsAppNotification(
        schedule?.clientId,
        "paid_cancellation_session2",
        params
      );

      let updatedSchedule;
      if (cancelled_session === "this session only") {
        updatedSchedule = await ScheduleDao.cancelSchedule(
          therapistId,
          recurrenceDateId
        );
      } else if (cancelled_session === "cancel entire slot") {
        updatedSchedule = await ScheduleDao.cancelAllSchedule(
          therapistId,
          schedule.clientId._id,
          recurrenceDateId
        );

        if (updatedSchedule) {
          await TherapistController.removeCalendarEvents(
            therapistId,
            schedule.recurrenceDates
          );
        }

        if (!updatedSchedule) {
          return res.status(400).json({
            success: false,
            message: "Unable to cancel appointment.",
          });
        }
      }
      res.send("Appointment Cancelled.");
    } catch (error) {
      next(error);
    }
  }

  static async removeCalendarEvents(
    therapistId: string,
    recurrenceDates: any[]
  ) {
    const batchSize = 20;
    const delay = 200;

    const eventsToRemove = recurrenceDates.filter(
      (event: { calenderEventId: string }) => event.calenderEventId
    );

    for (let i = 0; i < eventsToRemove.length; i += batchSize) {
      const batch = eventsToRemove.slice(i, i + batchSize);
      let retry = false;

      for (let attempt = 0; attempt < 2; attempt++) {
        try {
          await Promise.all(
            batch.map((event: { calenderEventId: string }) =>
              GoogleCalendarService.removeEventFromCalendar(therapistId, event)
            )
          );
          break;
        } catch (error: unknown) {
          if (
            error instanceof Error &&
            "response" in error &&
            (error as any).response?.status === 429
          ) {
            if (!retry) {
              retry = true;
              await new Promise((resolve) => setTimeout(resolve, 1000));
            } else {
              break;
            }
          } else {
            break;
          }
        }
      }

      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  static async cancelAllSchedule(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const clientId = req.params.id;

      // Use array filters to update all recurrenceDates elements
      const schedule = await ScheduleModel.updateMany(
        { therapistId: String(therapistId), clientId: String(clientId) },
        {
          $set: { "recurrenceDates.$[elem].status": "cancelled" },
        },
        {
          arrayFilters: [{ "elem.status": { $ne: "cancelled" } }], // Only update if status is not already "cancelled"
          multi: true, // Ensure all matching documents are updated
        }
      );

      if (schedule.matchedCount === 0) {
        return res.status(404).json({
          success: false,
          message: "No schedules found for this client.",
        });
      }

      return res.status(200).json({
        success: true,
        message: "All sessions cancelled successfully.",
      });
    } catch (error) {
      console.error(error);
      next(error);
    }
  }

  static async getClientSchedules(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const scheduleId = req.params.id;
      const scheduleAppointment: any = await ScheduleDao.getById(
        therapistId,
        scheduleId
      );
      if (!scheduleAppointment) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to find appointment." });
      }
      let reqData = [];
      for (let recurrenceDate of scheduleAppointment.recurrenceDates) {
        const scheduleData = {
          _id: scheduleAppointment?._id,
          email: scheduleAppointment?.email,
          additionalEmails: scheduleAppointment?.additionalEmails,
          scheduleId: scheduleAppointment?.scheduleId,
          name: scheduleAppointment?.name,
          fromDate: recurrenceDate.fromDate,
          toDate: recurrenceDate?.toDate,
          status: recurrenceDate?.status,
          therapistId: scheduleAppointment?.therapistId,
          clientCountry: scheduleAppointment?.clientCountry,
          recurrence: scheduleAppointment?.recurrence,
          isActive: scheduleAppointment?.isActive,
          syncWithCalender: scheduleAppointment?.syncWithCalender,
          tillDate: scheduleAppointment?.tillDate,
          description: scheduleAppointment?.description,
          createdAt: scheduleAppointment?.createdAt,
          updatedAt: scheduleAppointment?.updatedAt,
          recurrenceDateId: recurrenceDate._id,
          phone: scheduleAppointment?.phone,
          amount: recurrenceDate?.amount,
        };
        reqData.push(scheduleData);
      }
      res.send({
        client: {
          email: scheduleAppointment?.email,
          additionalEmails: scheduleAppointment?.additionalEmails,
          name: scheduleAppointment?.name,
          clientCountry: scheduleAppointment?.clientCountry,
          phone: scheduleAppointment?.phone,
        },
        appointments: reqData,
      });
    } catch (error) {
      next(error);
    }
  }

  static async searchClient(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const searchBy = req.query.searchBy;
      const searchText = req.query.searchText;
      const therapistId = req.therapist._id;
      if (searchBy == "name") {
        const clients = await ClientDao.searchByName(therapistId, searchText);
        if (!clients || clients.length == 0) {
          return res
            .status(400)
            .json({ success: false, message: "No client found." });
        }
        res.send({ clients: clients, clientCount: clients.length });
      }
      if (searchBy == "email") {
        const clients = await ClientDao.searchByEmail(therapistId, searchText);
        if (!clients || clients.length == 0) {
          return res
            .status(400)
            .json({ success: false, message: "No client found." });
        }
        res.send({ clients: clients, clientCount: clients.length });
      }
      if (!searchBy) {
        const clients = await ClientDao.searchByEmailOrNameWithPagination(
          therapistId,
          searchText,
          skip,
          pageSize
        );
        const totalClients = await ClientDao.searchByEmailOrName(
          therapistId,
          searchText
        );
        if (!clients || clients.length == 0) {
          return res
            .status(400)
            .json({ success: false, message: "No client found." });
        }
        res.send({ clients: clients, clientCount: totalClients.length });
      }
    } catch (error) {
      next(error);
    }
  }

  static async uploadTherapistProfile(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      if (!req.files?.length)
        return res
          .status(404)
          .json({ success: false, message: "No File uploaded" });
      let filesData: any = req.files;

      let allDocIds: any = [];

      for (let file of filesData) {
        const blob = fs.readFileSync(file.path);
        // const fileName = await Utility.createRandamAlphanumericString(7) + file.originalname;
        const fileName = String(therapistId);

        const fileUpload = await FileUploadService.uploadFile(blob, fileName);
        if (!fileUpload)
          return res.status(400).json({
            success: false,
            message: "There was some problem Uploading!",
          });

        allDocIds.push(fileUpload.Location);
      }
      const therapist: any = await TherapistService.getTherapist(therapistId);
      therapist.s3ProfilePhoto = true;
      await therapist.save();
      res.send(allDocIds);
    } catch (error) {
      next(error);
    }
  }

  static async clientCount(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      // let startDate: any = req.query.startDate
      //   ? String(req.query.startDate)
      //   : undefined;
      // let endDate: any = req.query.endDate
      //   ? String(req.query.endDate)
      //   : undefined;
      let startDate: any = new Date(req.query.startDate as string);
      let endDate: any = new Date(req.query.endDate as string);
      const therapistId = req.therapist._id;

      if (!startDate || !endDate) {
        return res.status(400).send({
          success: false,
          message: "Start date and end date are required",
        });
      }

      startDate = moment(startDate).toISOString();
      endDate = moment(endDate).toISOString();

      const totalClients = await ClientService.getAllClientsByTherapist(
        therapistId
      );

      let stats = {
        active: 0,
        in_active: 0,
        new: 0,
      };

      for (let client of totalClients) {
        const clientData: any = client;
        if (client.isActive) {
          stats.active += 1;
        } else {
          stats.in_active += 1;
        }
        if (moment(clientData.createdAt).isBetween(startDate, endDate)) {
          stats.new += 1;
        }
      }

      res.send({ stats });
    } catch (error) {
      next(error);
    }
  }

  static async sentEmailTherapist(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const { transactionId, eventId } = req.body;
      const transaction = await TransactionService.getTransactionById(
        transactionId
      );
      if (!transaction) {
        return res
          .status(404)
          .json({ success: false, message: "No transaction found." });
      }
      const event: any = await GoogleCalendarService.getEventById(eventId);
      if (!event) {
        return res
          .status(404)
          .json({ success: false, message: "No event found." });
      }

      const senderData = {
        name: event.therapistId.name,
        email: event.therapistId.email,
      };

      const receiverData = event.attendees.map((data: any) => {
        return {
          email: data.email,
        };
      });

      // const sentMail =  await Mailer2.sendMail(senderData, receiverData, subject, htmlTemplate);
    } catch (err) {
      next(err);
    }
  }

  static async getTherapistPayments(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const therapistId = req.therapist._id;
      const status = req.query.status;
      const name: any = req.query.name;
      const startDate: any = req.query.startDate;
      const endDate: any = req.query.endDate;
      // const searchText = String(req.query.searchText);
      let searchText = undefined;
      if (req.query.searchText != undefined) {
        searchText = String(req.query.searchText);
      }
      const amount: any = req.query.amount;
      const client: any = req.query.client;
      const payments: any = await TransactionService.getTherapistTransactions(
        therapistId,
        client
      );
      let totalPayments: any = [];
      let requiredPayments;
      let count;
      for (let payment of payments) {
        const schedule = await ScheduleDao.getById(
          therapistId,
          payment.scheduleId
        );
        const reccuranceData = schedule?.recurrenceDates.find(
          (recurrenceDate: any) =>
            String(recurrenceDate._id) == String(payment.scheduleRecId)
        );
        await totalPayments.push({
          clientName: payment?.clientId?.name,
          clientId: payment?.clientId?.clientId,
          _id: payment?._id,
          paymentStatus: payment?.paymentStatus,
          customerId: payment?.clientId,
          paymentLink: payment?.paymentLink,
          createdAt: payment?.createdAt,
          updatedAt: payment?.updatedAt,
          amount: payment?.amount,
          therapistId: payment?.therapistId,
          amountRecieved: payment?.amountReceived,
          paymentDate: payment?.paymentDate,
          paymentMethod: payment?.paymentMethod,
          therapistUpiApproved: req.therapist?.bankDetails?.upiApprove,
          sessionDate: reccuranceData?.toDate,
          recc_id: reccuranceData?._id,
          isFine: payment?.isFine,
        });
      }
      if (!status) {
        const payments = await TransactionService.getPayments(
          totalPayments,
          name,
          startDate,
          endDate,
          amount,
          skip,
          pageSize
        );
        count = payments.count;
        requiredPayments = payments.requiredPayments;
      }
      if (status == "recieved") {
        const recievedPayments = await totalPayments.filter(
          (payment: any) =>
            payment.paymentStatus == paymentStatus.COMPLETE ||
            payment.paymentStatus == paymentStatus.PAID_OFFLINE
        );
        const payments = await TransactionService.getPayments(
          recievedPayments,
          name,
          startDate,
          endDate,
          amount,
          skip,
          pageSize
        );
        count = payments.count;
        requiredPayments = payments.requiredPayments;
      }
      if (status == "pending") {
        const pendingPayments = await totalPayments.filter(
          (payment: any) => payment.paymentStatus == paymentStatus.PENDING
        );
        const payments = await TransactionService.getPayments(
          pendingPayments,
          name,
          startDate,
          endDate,
          amount,
          skip,
          pageSize
        );
        count = payments.count;
        requiredPayments = payments.requiredPayments;
      }
      if (status == "paidOffline") {
        const paidOfflinePayments = await totalPayments.filter(
          (payment: any) => payment.paymentStatus == paymentStatus.PAID_OFFLINE
        );
        const payments = await TransactionService.getPayments(
          paidOfflinePayments,
          name,
          startDate,
          endDate,
          amount,
          skip,
          pageSize
        );
        count = payments.count;
        requiredPayments = payments.requiredPayments;
      }
      if (searchText && searchText != undefined) {
        const clients = await ClientDao.searchByEmailOrName(
          therapistId,
          searchText
        );
        let allPayments = [];
        for (let client of clients) {
          const payments: any = await TransactionDao.getTransactionWithClientId(
            therapistId,
            client._id
          );
          if (payments.length == 0) {
            continue;
          }
          for (let payment of payments) {
            const schedule = await ScheduleDao.getById(
              therapistId,
              payment.scheduleId
            );
            const reccuranceData = schedule?.recurrenceDates.find(
              (recurrenceDate: any) =>
                String(recurrenceDate._id) == String(payment.scheduleRecId)
            );
            await allPayments.push({
              clientName: payment?.clientId?.name,
              _id: payment?._id,
              paymentStatus: payment?.paymentStatus,
              customerId: payment?.clientId,
              paymentLink: payment?.paymentLink,
              createdAt: payment?.createdAt,
              updatedAt: payment?.updatedAt,
              amount: payment?.amount,
              therapistId: payment?.therapistId,
              amountRecieved: payment?.amountReceived,
              paymentDate: payment?.paymentDate,
              paymentMethod: payment?.paymentMethod,
              sessionDate: reccuranceData?.toDate,
              therapistUpiApproved: req.therapist?.bankDetails?.upiApprove,
              recc_id: reccuranceData?._id,
              isFine: payment?.isFine,
            });
          }
        }
        if (status == "recieved") {
          const recievedPayments = await allPayments.filter(
            (payment: any) =>
              payment.paymentStatus == paymentStatus.COMPLETE ||
              payment.paymentStatus == paymentStatus.PAID_OFFLINE
          );
          const payments = await TransactionService.getPayments(
            recievedPayments,
            name,
            startDate,
            endDate,
            amount,
            skip,
            pageSize
          );
          count = payments.count;
          requiredPayments = payments.requiredPayments;
        }
        if (status == "pending") {
          const pendingPayments = await allPayments.filter(
            (payment: any) => payment.paymentStatus == paymentStatus.PENDING
          );
          const payments = await TransactionService.getPayments(
            pendingPayments,
            name,
            startDate,
            endDate,
            amount,
            skip,
            pageSize
          );
          count = payments.count;
          requiredPayments = payments.requiredPayments;
        }
        if (!status) {
          const payments = await TransactionService.getPayments(
            allPayments,
            name,
            startDate,
            endDate,
            amount,
            skip,
            pageSize
          );
          count = payments.count;
          requiredPayments = payments.requiredPayments;
        }
        // count = allPayments.length;
        // requiredPayments = await allPayments.slice(skip, skip + pageSize);
      }
      res.send({ payments: requiredPayments, paymentsCount: count });
    } catch (error) {
      next(error);
    }
  }

  static async getTherapistPaymentStats(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const month = Number(req.query.month);
      const currentDate = new Date();
      const therapistId = req.therapist._id;
      const totalCompletedPayments =
        await TransactionService.getTherapistCompleteTransactions(therapistId);
      const totalMarkPaidOfflinePayments =
        await TransactionService.getTherapistMarkPaidOfflineTransactionset(
          therapistId
        );
      // const totalPendingPayments = await TransactionService.getTherapistPendingTransactions(therapistId);
      let firstDayOfMonth = new Date(
        currentDate.getFullYear(),
        month - 1,
        1
      ).toISOString();
      let lastDayOfMonth = new Date(
        currentDate.getFullYear(),
        month,
        0
      ).toISOString();
      const totalPendingPayments =
        await TransactionService.getTherapistPendingTransactionsInMonth(
          therapistId,
          firstDayOfMonth,
          lastDayOfMonth
        );
      const totalEarnings = totalCompletedPayments.reduce(
        (total, current) => total + Number(current.amount),
        0
      );
      const totalMarkPaidOffline = totalMarkPaidOfflinePayments.reduce(
        (total, current) => total + Number(current.amount),
        0
      );
      const pendingPaymentAmount = totalPendingPayments.reduce(
        (total, current) => total + Number(current.amount),
        0
      );
      const earningsInAMonth = await TransactionService.getTransactionsOfAMonth(
        therapistId,
        firstDayOfMonth,
        lastDayOfMonth
      );
      const monthEarnings = earningsInAMonth.reduce(
        (total, current) => total + Number(current.amount),
        0
      );
      res.send({
        totalEarnings,
        pendingPaymentAmount,
        monthEarnings,
        totalMarkPaidOffline,
      });
    } catch (error) {
      next(error);
    }
  }

  // static async getDashboardStats(req: express.Request, res: express.Response, next: express.NextFunction) {
  //     try {

  //         const sessionCount = Number(req.query.sessionCountDays) || 30
  //         const clientCountDays = Number(req.query.clientCountDays) || 30

  //         const currentDate = new Date();
  //         const currentMonthNumeric = moment().month() + 1;
  //         let firstDayOfMonth = new Date(currentDate.getFullYear(), currentMonthNumeric - 1, 1).toISOString();
  //         let lastDayOfMonth = new Date(currentDate.getFullYear(), currentMonthNumeric, 0).toISOString();
  //         const therapistId = req.therapist._id;
  //         const startOfWeek = moment().startOf('week');
  //         const clonedStartOfWeek = startOfWeek.clone()
  //         const beforeLastTwoWeeks = clonedStartOfWeek.subtract(sessionCount, "days").toISOString();
  //         const lastThirtyDays = moment().subtract(clientCountDays, "days").toISOString();
  //         const schedules: any = await ScheduleDao.getAllRecurrenceDates(therapistId);
  //         let allRecurrenceDates: any = []
  //         for (let schedule of schedules) {
  //             allRecurrenceDates.push(schedule.recurrenceDates)
  //         }
  //         const reqRecurrenceDates: any = await [].concat(...allRecurrenceDates);
  //         const schedulesLastTwoWeeks = await reqRecurrenceDates.filter((recDate: any) => new Date(recDate.toDate).toISOString() > new Date(beforeLastTwoWeeks).toISOString() && new Date(recDate.toDate).toISOString() < startOfWeek.toISOString())
  //         // const clients = await ClientDao.getAllTherapistClientsWithDate(therapistId, lastThirtyDays, currentDate.toISOString())
  //         const clients = await ClientDao.getAllTherapistClientsWithDate(therapistId)
  //         const totalCompletedPayments = await TransactionService.getTransactionsOfAMonth(therapistId, firstDayOfMonth, lastDayOfMonth);
  //         const totalPendingPayments = await TransactionDao.getPendingTransactionsOfAMonth(therapistId, firstDayOfMonth, lastDayOfMonth);
  //         const totalEarnings = totalCompletedPayments.reduce((total, current) => total + Number(current.amount), 0)
  //         const pendingPaymentAmount = totalPendingPayments.reduce((total, current) => total + Number(current.amount), 0)
  //         res.send({ schedules: schedulesLastTwoWeeks?.length, clients: clients?.length, pendingPayments: pendingPaymentAmount, receivedPayment: totalEarnings })
  //     }
  //     catch (error) {
  //         next(error)
  //     }
  // }

  // static async getDashboardStats(
  //   req: express.Request,
  //   res: express.Response,
  //   next: express.NextFunction
  // ) {
  //   try {
  //     const startDate = new Date(req.query.startDate as string);
  //     const endDate = new Date(req.query.endDate as string);

  //     if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
  //       return res
  //         .status(400)
  //         .json({ success: false, message: "Invalid startDate or endDate." });
  //     }

  //     const therapistId = req.therapist._id;

  //     const schedules = await ScheduleDao.getAllRecurrenceDates(therapistId);

  //     const allRecurrenceDates = schedules.flatMap(
  //       (schedule) => schedule.recurrenceDates
  //     );
  //     const schedulesInRange: any = allRecurrenceDates.filter((recDate) => {
  //       const date = new Date(recDate.toDate);
  //       return date >= startDate && date <= endDate;
  //     });

  //     console.log(schedulesInRange);

  //     const clients = await ClientDao.getAllTherapistClientsWithDate(
  //       therapistId,
  //       startDate.toISOString(),
  //       endDate.toISOString()
  //     );

  //     const completedPayments =
  //       await TransactionService.getTransactionsOfAMonth(
  //         therapistId,
  //         startDate.toISOString(),
  //         endDate.toISOString()
  //       );

  //     const pendingPayments =
  //       await TransactionDao.getPendingTransactionsOfAMonth(
  //         therapistId,
  //         startDate.toISOString(),
  //         endDate.toISOString()
  //       );

  //     const totalEarnings = completedPayments.reduce(
  //       (total, payment) => total + Number(payment.amount),
  //       0
  //     );

  //     const pendingPaymentAmount = pendingPayments.reduce(
  //       (total, payment) => total + Number(payment.amount),
  //       0
  //     );

  //     res.send({
  //       schedules: schedulesInRange.length,
  //       clients: clients.length,
  //       receivedPayment: totalEarnings,
  //       pendingPayments: pendingPaymentAmount,
  //     });
  //   } catch (error) {
  //     next(error);
  //   }
  // }

  static async getDashboardStats(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const startDate = new Date(req.query.startDate as string);
      const endDate = new Date(req.query.endDate as string);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return res
          .status(400)
          .json({ success: false, message: "Invalid startDate or endDate." });
      }

      const therapistId = req.therapist._id;

      // Get all recurrence dates within the range
      const schedules = await ScheduleDao.getAllRecurrenceDates(therapistId);
      const allRecurrenceDates = schedules.flatMap(
        (schedule) => schedule.recurrenceDates
      );

      const schedulesInRange: any = allRecurrenceDates.filter((recDate) => {
        const date = new Date(recDate.fromDate);
        return (
          date >= startDate && date <= endDate && recDate.status !== "cancelled"
        );
      });

      // Get all clients within the date range
      const clients = await ClientDao.getAllTherapistClientsWithDate(
        therapistId,
        startDate.toISOString(),
        endDate.toISOString()
      );

      // Calculate completed, pending, paid on time, and paid on delay payments
      // const completedPayments =
      //   await TransactionService.getTransactionsOfAMonth(
      //     therapistId,
      //     startDate.toISOString(),
      //     endDate.toISOString()
      //   );

      // // Calculate total earnings
      // const totalEarnings = completedPayments.reduce(
      //   (total, payment) => total + Number(payment.amount),
      //   0
      // );

      // Fetch and calculate totals from PayTrackerModel for 'Paid on time' and 'Paid on delay'
      const paidOnTimePayments = await PayTrackerModel.find({
        therapistId: therapistId,
        status: { $in: ["Paid on time", "Paid Delay"] }, // Include both "Paid on time" and "Paid on delay"
        paymentDate: { $gte: startDate, $lte: endDate }, // Filter by payment date range
      });

      const totalPayment = paidOnTimePayments.reduce(
        (total, payment) => total + Number(payment.amount.value),
        0
      );

      const cancelledPayments = await PayTrackerModel.find({
        therapistId: therapistId,
        status: { $in: ["Paid Cancellation"] }, // Include both "Paid on time" and "Paid on delay"
        updatedAt: { $gte: startDate, $lte: endDate }, // Filter by payment date range
      });

      const totalCancelled = cancelledPayments.reduce(
        (total, payment) => total + Number(payment.cancellationFee.value),
        0
      );

      const totalEarnings = totalPayment + totalCancelled;

      // Fetch and calculate totals for "Still pending" payments
      // const stillPendingPayments = await PayTrackerModel.find({
      //   therapistId: therapistId,
      //   status: "Still pending",
      //   updatedAt: { $gte: startDate, $lte: endDate }, // Filter by payment date range
      // });

      // const stillPendingTotal = stillPendingPayments.reduce(
      //   (total, payment) => total + Number(payment.amount.value),
      //   0
      // );

      const pending = await ClientDao.getPending(
        therapistId,
        startDate,
        endDate
      );

      // Return response with the calculated data
      res.send({
        schedules: schedulesInRange.length,
        clients: clients.length,
        receivedPayment: totalEarnings,
        pendingPayments: pending,
      });
    } catch (error) {
      next(error);
    }
  }

  static async sendReminder(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const transaction_id = req.params.paymentId;

      const transaction = await TransactionService.getTransactionBy_Id(
        transaction_id,
        req.therapist._id
      );
      if (!transaction) {
        return res
          .status(400)
          .json({ success: false, message: "No transaction found." });
      }
      const schedule: any = await ScheduleDao.getScheduleByRecurrenceDateId(
        req.therapist._id,
        transaction.scheduleRecId
      );
      if (!schedule) {
        return res
          .status(400)
          .json({ success: false, message: "No schedule found." });
      }
      const reccuranceData = schedule?.recurrenceDates.find(
        (recurrenceDate: any) =>
          String(recurrenceDate._id) == String(transaction.scheduleRecId)
      );
      if (!reccuranceData) {
        return res
          .status(400)
          .json({ success: false, message: "No schedule recurrence found." });
      }

      let senderData = {
        email: req.therapist.email,
        name: req.therapist.name,
      };
      let receiverData = [
        {
          email: schedule?.clientId?.email,
          name: schedule.clientId?.name || "There",
        },
      ];
      let htmlTemplate = scheduleReminder({
        clientName: schedule?.clientId?.name || "There",
        therapistName: req.therapist.name,
        scheduleDate: reccuranceData.fromDate,
        meetingLink: reccuranceData.meetLink,
        // paymentLink: transaction.paymentLink,
        // payLater: reccuranceData.payLater,
        timezone: schedule?.clientId?.defaultTimezone,
        amount: reccuranceData.amount,
      });

      let subject = MailSubjectEnum.REMAINDER;
      const sentEmail = await Mailer2.sendMail(
        senderData,
        receiverData,
        subject,
        htmlTemplate
      );
      if (!sentEmail) {
        return res.status(400).json({ success: false, message: "Failed" });
      }

      const formattedDate = format(
        new Date(reccuranceData.fromDate),
        "dd-MM-yyyy"
      );
      const formattedTime = format(new Date(reccuranceData.fromDate), "HH:mm");

      const params = {
        CLIENT_NAME: schedule?.clientId.name,
        THERAPIST_NAME: req?.therapist?.name,
        SESSION_DATE: formattedDate,
        SESSION_TIME: formattedTime,
        MEETLINK: reccuranceData.meetLink,
      };

      await sendWhatsAppNotification(
        schedule?.clientId,
        "session_reminder",
        params
      );

      res.send("OK");
    } catch (error) {
      next(error);
    }
  }

  static async searchSchedules(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const therapistId = req.therapist._id;
      const searchText = String(req.query.searchText);
      const schedules = await ScheduleDao.search(therapistId, searchText);
      let allRecurrenceDates: any = [];
      for (let schedule of schedules) {
        allRecurrenceDates.push(schedule.recurrenceDates);
      }
      const reqRecurrenceDates: any = [].concat(...allRecurrenceDates);
      const latestRecurrenceDates = reqRecurrenceDates.sort(
        (a: any, b: any) => a.toDate - b.toDate
      );
      let reqData = [];
      for (let recurrenceDate of latestRecurrenceDates) {
        const schedule: any = await ScheduleDao.getByRecurrenceDateId(
          recurrenceDate._id
        );
        const scheduleData = {
          _id: schedule?._id,
          email: schedule?.email,
          additionalEmails: schedule?.additionalEmails,
          scheduleId: schedule?.scheduleId,
          name: schedule?.name,
          fromDate: recurrenceDate?.fromDate,
          toDate: recurrenceDate?.toDate,
          status: recurrenceDate?.status,
          therapistId: schedule?.therapistId,
          clientCountry: schedule?.clientCountry,
          recurrence: schedule?.recurrence,
          isActive: schedule?.isActive,
          syncWithCalender: schedule?.syncWithCalender,
          tillDate: schedule?.tillDate,
          description: schedule?.description,
          createdAt: schedule?.createdAt,
          updatedAt: schedule?.updatedAt,
          recurrenceDateId: recurrenceDate._id,
          phone: schedule?.phone,
          amount: recurrenceDate?.amount,
        };
        reqData.push(scheduleData);
      }
      const paginationData = await reqData.slice(skip, skip + pageSize);
      res.send({
        appointments: paginationData,
        appointmentsCount: reqData.length,
      });
    } catch (error) {
      next(error);
    }
  }

  static async searchTherapistPayment(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const pageNumber: any = Number(req.query.pageNumber) || 1;
      const pageSize: any = Number(req.query.pageSize) || 20;
      const skip = (pageNumber - 1) * pageSize;
      const therapistId = req.therapist._id;
      const searchText = String(req.query.searchText);
      const clients = await ClientDao.searchByEmailOrName(
        therapistId,
        searchText
      );
      let totalPayments = [];
      for (let client of clients) {
        const payments: any = await TransactionDao.getTransactionWithClientId(
          therapistId,
          client._id
        );
        if (payments.length == 0) {
          continue;
        }
        for (let payment of payments) {
          await totalPayments.push({
            clientName: payment?.clientId?.name,
            _id: payment?._id,
            paymentStatus: payment?.paymentStatus,
            customerId: payment?.clientId,
            paymentLink: payment?.paymentLink,
            createdAt: payment?.createdAt,
            updatedAt: payment?.updatedAt,
            amount: payment?.amount,
            therapistId: payment?.therapistId,
            amountRecieved: payment?.amountReceived,
            paymentDate: payment?.paymentDate,
            paymentMethod: payment?.paymentMethod,
          });
        }
      }
      const paginationData = await totalPayments.slice(skip, skip + pageSize);
      res.send({
        payments: paginationData,
        paymentsCount: totalPayments.length,
      });
    } catch (error) {
      next(error);
    }
  }

  static async markScheduleCompleted(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const recurrenceDateId = req.params.id;
      const updatedSchedule = await ScheduleDao.markScheduleCompleted(
        therapistId,
        recurrenceDateId
      );
      if (!updatedSchedule) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to update appointment." });
      }
      res.send("Appointment Completed.");
    } catch (error) {
      next(error);
    }
  }

  // static async recreatePaymentLink(req: express.Request, res: express.Response, next: express.NextFunction) {
  //     try {
  //         const transaction_id = req.params.id;
  //         const transaction = await TransactionService.getTransactionBy_Id(transaction_id, req.therapist._id);
  //         if(!transaction){
  //             return res.status(404).send("No transaction found.")
  //         }
  //         if(transaction.paymentStatus == paymentStatus.COMPLETE){
  //             return res.status(500).send("Transaction already completed.")
  //         }
  //         const paymentLink = await RazorpayService.recreateLink(transaction_id);
  //         if(!paymentLink){
  //             return res.status(500).send("Unable to create payment link.")
  //         }
  //         res.send("Payment Link re-generated.")
  //     }
  //     catch (error) {
  //         next(error)
  //     }
  // }

  static async sendOtp(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const email = req.body.email;
      const existingEmail = await TherapistDao.findByEmail(email);
      if (!existingEmail) {
        return res.status(400).json({
          success: false,
          message: "No Therapist found with this email.",
        });
      }
      const otp = await Utility.generateOTP();
      let senderData = {
        email: CONFIG.companyEmail,
        name: CONFIG.companyName,
      };
      let htmlTemplate = sendOtpForPassword(
        existingEmail?.name || "There",
        otp
      );
      let receiverData = [
        {
          email: email,
          name: existingEmail?.name || "There",
        },
      ];

      let subject = MailSubjectEnum.OTP_VERIFICATION;
      const sentEmail = await Mailer2.sendMail(
        senderData,
        receiverData,
        subject,
        htmlTemplate
      );
      if (!sentEmail) {
        return res
          .status(400)
          .json({ success: false, message: "Sending Email Failed" });
      }
      existingEmail.otpData.otp = otp;
      existingEmail.otpData.validTill = new Date(
        moment().add(30, "minutes").toISOString()
      );
      await existingEmail.save();

      // await sendWhatsAppNotification(existingEmail?.phone, whatsappTemplateId);
      // return res.status(200).send("Updated");

      res.send("OK");
    } catch (error) {
      next(error);
    }
  }

  static async verifyOtp(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const email = req.body.email;
      const otp = req.body.otp;
      const existingEmail = await TherapistDao.findByEmail(email);
      if (!existingEmail) {
        return res.status(400).json({
          success: false,
          message: "No Therapist found with this email.",
        });
      }
      const isOtpValid = moment().isBefore(
        moment(existingEmail.otpData.validTill)
      );
      if (!isOtpValid) {
        return res
          .status(400)
          .json({ success: false, message: "OTP has expired." });
      }
      if (String(existingEmail.otpData.otp) != String(otp)) {
        return res.status(400).json({ success: false, message: "Wrong OTP." });
      }
      res.send("OK");
    } catch (error) {
      next(error);
    }
  }

  static async updatePassword(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const email = req.body.email;
      const password = req.body.password;
      const existingEmail = await TherapistDao.findByEmail(email);
      if (!existingEmail) {
        return res.status(400).json({
          success: false,
          message: "No Therapist found with this email.",
        });
      }
      const hexPassword = Utility.createPasswordHash(password);
      existingEmail.password = hexPassword;
      await existingEmail.save();
      res.send("OK");
    } catch (error) {
      next(error);
    }
  }

  static async uploadTherapistDocument(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      if (!req.files?.length)
        return res
          .status(404)
          .json({ success: false, message: "No File uploaded" });
      let filesData: any = req.files;

      let allDocIds: any = [];
      let docCount = 0;
      for (let file of filesData) {
        const blob = fs.readFileSync(file.path);
        docCount = docCount + 1;
        // const fileName = await Utility.createRandamAlphanumericString(7) + file.originalname;
        const fileName = String(therapistId) + "_doc_" + String(docCount);

        const fileUpload = await FileUploadService.uploadFile(blob, fileName);
        if (!fileUpload)
          return res.status(500).json({
            success: false,
            message: "There was some problem Uploading!",
          });

        allDocIds.push(fileUpload.Location);
      }
      const therapist: any = await TherapistService.getTherapist(therapistId);
      therapist.verificationDetails.uploadedDocsCount = docCount;
      await therapist.save();
      res.send(allDocIds);
    } catch (error) {
      next(error);
    }
  }

  static async updateClient(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const clientData = req.body;
      // const updatedClient = await ClientDao.updateClient(req.therapist._id, req.params.id, clientData);
      // if(!updatedClient){
      //     return res.status(500).send("Unable to update client name");
      // }
      const client = await ClientDao.getClientById(req.params.id);
      if (!client) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to update client name" });
      }

      if (
        clientData.gender &&
        !Object.values(GenderEnum).includes(clientData.gender)
      ) {
        return res.status(400).json({
          success: false,
          message: "Invalid gender",
        });
      }

      if (client.isActive === true && clientData.isActive == false) {
        const { success, message } = await ClientService.markClientInActive(
          client._id
        );

        if (!success) {
          return res.status(400).json({
            success: false,
            message: message || "Unable to mark client inactive.",
          });
        }
      }

      client.name = clientData.name;
      client.gender = clientData.gender;
      client.phone = clientData.phone;
      client.age = clientData.age;
      client.isActive = clientData.isActive;
      client.defaultSessionAmount = clientData.defaultSessionAmount;
      client.defaultTimezone = clientData.defaultTimezone;
      await client.save();

      if (clientData.defaultSessionAmount) {
        const data = await ClientDao.defaultSession(
          req.params.id,
          Number(clientData.defaultSessionAmount)
        );
      }
      res.send("Client Updated Successfully.");
    } catch (error) {
      next(error);
    }
  }

  static async updateTherapistVerification(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      if (!req.files?.length)
        return res
          .status(404)
          .json({ success: false, message: "No File uploaded" });
      let filesData: any = req.files;

      let allDocIds: any = [];
      let docCount = 0;
      for (let file of filesData) {
        const blob = fs.readFileSync(file.path);
        docCount = docCount + 1;

        const fileUpload = await FileUploadService.uploadFile(
          blob,
          file.filename
        );
        if (!fileUpload)
          return res.status(400).json({
            success: false,
            message: "There was some problem Uploading!",
          });

        allDocIds.push(fileUpload.Location);
      }
      let reqBody = req.body;
      let objectValues = Object.values(reqBody).map((value: any) =>
        JSON.parse(value)
      );
      const keys = Object.keys(reqBody);
      await keys.forEach((key, index) => {
        if (typeof reqBody[key] === "string") {
          reqBody[key] = objectValues[index];
        }
      });
      const payload = {
        verificationDetails: {
          agePreference: reqBody?.agePreference,
          genderPreference: reqBody?.genderPreference,
          practicingTitle: reqBody?.practicingTitle,
          clientLoad: reqBody?.clientLoad,
          yearsOfExperience: reqBody?.yearsOfExperience,
          featuresNeed: reqBody?.featuresNeed,
          source: reqBody?.source,
          uploadedDocsCount: docCount,
          sentForVerification: true,
          docs: allDocIds,
        },
        phone: reqBody?.phone,
      };
      const updateTherapist = await TherapistService.updateTherapistData(
        therapistId,
        payload
      );
      if (!updateTherapist) {
        return res
          .status(400)
          .json({ success: false, message: "Unable to update therapist." });
      }
      res.send("Upadated successfully.");
    } catch (error) {
      next(error);
    }
  }

  static async sendScheduleReminder(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therpaistId = req.therapist._id;
      const recurrenceDateId = req.params.id;

      const schedule: any = await ScheduleDao.getScheduleByRecurrenceDateId(
        therpaistId,
        recurrenceDateId
      );

      if (!schedule) {
        return res
          .status(400)
          .json({ success: false, message: "No schedule found." });
      }
      const reccuranceData = schedule?.recurrenceDates.find(
        (recurrenceDate: any) =>
          String(recurrenceDate._id) == String(recurrenceDateId)
      );
      if (!reccuranceData) {
        return res
          .status(400)
          .json({ success: false, message: "No schedule recurrence found." });
      }

      let paymentLink = undefined;
      if (reccuranceData.transactionId) {
        const transaction = await TransactionService.getTransactionById(
          reccuranceData?.transactionId
        );
        if (transaction) {
          paymentLink = transaction?.paymentLink;
        }
      }

      let senderData = {
        email: req.therapist.email,
        name: req.therapist.name,
      };
      let receiverData = [
        {
          email: schedule?.clientId?.email,
          name: schedule.clientId?.name || "There",
        },
      ];
      // let htmlTemplate = createScheduleTemplate({
      //   clientName: schedule?.clientId?.name,
      //   therapistName: req.therapist.name,
      //   scheduleDate: reccuranceData.fromDate,
      //   meetingLink: reccuranceData.meetLink,
      //   paymentLink: paymentLink,
      //   payLater: reccuranceData.payLater,
      //   timezone: schedule?.clientId?.defaultTimezone,
      //   amount: reccuranceData.amount,
      // });

      let htmlTemplate = scheduleReminder({
        clientName: schedule?.clientId?.name || "There",
        therapistName: req.therapist.name,
        scheduleDate: reccuranceData.fromDate,
        meetingLink: reccuranceData.meetLink,
        timezone: schedule?.clientId?.defaultTimezone,
        amount: reccuranceData.amount,
      });

      let subject = MailSubjectEnum.REMAINDER;
      const sentEmail = await Mailer2.sendMail(
        senderData,
        receiverData,
        subject,
        htmlTemplate
      );
      if (!sentEmail) {
        return res.status(400).json({ success: false, message: "Failed" });
      }

      const formattedDate = format(
        new Date(reccuranceData.fromDate),
        "dd-MM-yyyy"
      );
      const formattedTime = format(new Date(reccuranceData.fromDate), "HH:mm");

      const params = {
        CLIENT_NAME: schedule?.clientId.name,
        THERAPIST_NAME: req?.therapist?.name,
        SESSION_DATE: formattedDate,
        SESSION_TIME: formattedTime,
        MEETLINK: reccuranceData.meetLink,
      };

      await sendWhatsAppNotification(schedule, "session_reminder", params);

      res.send("OK");
    } catch (error) {
      next(error);
    }
  }

  static async createPaymentLink(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const scheduleRecId = req.params.id;
      const amount = req.body.amount;
      const schedule: any = await ScheduleDao.getScheduleByRecurrenceDateId(
        req.therapist._id,
        scheduleRecId
      );
      const reccuranceData = schedule?.recurrenceDates.find(
        (recurrenceDate: any) =>
          String(recurrenceDate._id) == String(scheduleRecId)
      );
      if (!reccuranceData) {
        return res
          .status(400)
          .json({ success: false, message: "No schedule recurrence found." });
      }

      // if(req.therapist.menus.paymentGateway){
      //     const transaction = await TransactionService.getTransactionByRecId(scheduleRecId, req.therapist._id);
      //     if (transaction) {
      //         return res.status(400).send("Payment Link already exists in " + transaction.paymentStatus + " state");
      //     } else {

      //         const paymentLink = await RazorpayService.createPaymentLink(amount, req.therapist._id, schedule?._id, scheduleRecId, schedule?.clientId?._id);
      //         if (!paymentLink) {
      //             return res.status(500).send("Unable to create payment link.")
      //         }

      //         const updateSchedule = await ScheduleDao.updateRecurrenceAmount(req.therapist._id, schedule._id, scheduleRecId, amount, paymentLink.transactionId)
      //         if (!updateSchedule) {
      //             return res.status(500).send("Unable to update schedule.")
      //         }

      //         let senderData = {
      //             email: req.therapist.email,
      //             name: req.therapist.name
      //         }
      //         let receiverData = [{
      //             email: schedule?.clientId?.email,
      //             name: schedule.clientId?.name
      //         }]

      //         const htmlTemplate = createScheduleTemplate({
      //             clientName: schedule?.clientId?.name,
      //             therapistName: req.therapist.name,
      //             scheduleDate: reccuranceData.fromDate,
      //             meetingLink: reccuranceData.meetLink,
      //             paymentLink: paymentLink.paymentLink,
      //             payLater: true,
      //             timezone: schedule?.clientId?.defaultTimezone
      //         })
      //         let subject = MailSubjectEnum.PAYMENT_REMAINDER;

      //         const sentEmail = await Mailer2.sendMail(senderData, receiverData, subject, htmlTemplate);
      //         if (!sentEmail) {
      //             return res.status(400).send("Failed")
      //         }
      // }

      // }

      if (req.therapist.menus.paymentTracker) {
        const paymentTracker =
          await PayTrackerService.getPaytrackerByScheduleIdAndScheduleRecId(
            schedule._id,
            reccuranceData._id
          );
        if (paymentTracker) {
          return res.status(400).json({
            success: false,
            message: "Payment Tracker already exists.",
          });
        }

        const payload = {
          therapistId: req.therapist._id,
          scheduleId: schedule?._id,
          scheduleRecId: reccuranceData?._id,
          clientId: schedule?.clientId?._id,
          dueDate: reccuranceData.fromDate,
          amount: {
            currency: "INR",
            value: amount,
          },
          paymentType: reccuranceData.payLater
            ? PaymentTrackerTypeEnum.Post_Session
            : PaymentTrackerTypeEnum.Advance,
          status: PaymentTrackerStatusEnum.Still_Pending,
          paymentDate: undefined,
          isDeleted: false,
          tags: [schedule?.clientId.name],
          isFine: false,
          cancellationFee: {
            currency: "INR",
            value: 0,
          },
        };

        const newPaytracker = await PayTrackerService.createPayTracker(payload);
        if (!newPaytracker) {
          return res
            .status(400)
            .json({ success: false, message: "Unable to create paytracker." });
        }

        const updateSchedule =
          await ScheduleDao.updateRecurrenceAmountForPayTracker(
            req.therapist._id,
            schedule._id,
            scheduleRecId,
            amount,
            newPaytracker._id
          );
        if (!updateSchedule) {
          return res
            .status(400)
            .json({ success: false, message: "Unable to update schedule." });
        }

        res.send("Payment generated.");
      }
    } catch (error) {
      next(error);
    }
  }

  static async getTransactionByRecIdTherapistId(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const scheduleRecId = req.params.scheduleRecId;
      const therpaistId = req.therapist._id;

      const transactionDetails =
        await TransactionService.getTransactionByRecIdTherapistId(
          scheduleRecId,
          therpaistId
        );

      if (!transactionDetails) {
        return res
          .status(400)
          .json({ success: false, message: "Cannot Update Payment Link" });
      }
      if (
        !transactionDetails.paymentSessionId ||
        !transactionDetails.paymentLink
      ) {
        return res
          .status(400)
          .json({ success: false, message: "Cannot Update Payment Link" });
      }
      res.send(
        new Response({ transactionDetails }, "Data fetched success", 200)
      );
    } catch (err) {
      next(err);
    }
  }

  static async updateTransactionByRecIdTherapistId(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const scheduleRecId = req.params.scheduleRecId;
      const therpaistId = req.therapist._id;
      const payload = req.body;
      const transactionDetails =
        await TransactionService.updateTransactionByRecIdTherapistId(
          scheduleRecId,
          therpaistId,
          payload
        );

      if (!transactionDetails) {
        return res.status(400).json({
          success: false,
          message: "Unable to fetch data, Internal server error",
        });
      }

      res.send(
        new Response({ transactionDetails }, "Data fetched success", 200)
      );
    } catch (err) {
      next(err);
    }
  }

  static async recreatePaymentLink(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const scheduleRecId = req.params.id;
      const amount = req.body.amount;
      const schedule: any = await ScheduleDao.getScheduleByRecurrenceDateId(
        req.therapist._id,
        scheduleRecId
      );
      const reccuranceData = schedule?.recurrenceDates.find(
        (recurrenceDate: any) =>
          String(recurrenceDate._id) == String(scheduleRecId)
      );
      if (!reccuranceData) {
        return res
          .status(400)
          .json({ success: false, message: "No schedule recurrence found." });
      }

      if (req.therapist.menus.paymentGateway) {
        const transaction = await TransactionService.getTransactionByRecId(
          scheduleRecId,
          req.therapist._id
        );
        if (!transaction) {
          return res
            .status(400)
            .json({ success: false, message: "Payment Link Not Found." });
        }

        if (transaction.paymentStatus == paymentStatus.COMPLETE) {
          return res
            .status(400)
            .json({ success: false, message: "Payment already completed." });
        }

        if (transaction.paymentStatus == paymentStatus.PAID_OFFLINE) {
          return res
            .status(400)
            .json({ success: false, message: "Payment already paid offline." });
        }

        const cancelPaymentLink = await RazorpayService.cancelPaymentLink(
          transaction.paymentSessionId
        );
        if (!cancelPaymentLink) {
          return res.status(400).json({
            success: false,
            message: "Unable to cancel payment link.",
          });
        }

        transaction.paymentStatus = paymentStatus.CANCELLED;
        await transaction.save();
        const paymentLink = await RazorpayService.createPaymentLink(
          Number(amount),
          req.therapist._id,
          schedule?._id,
          scheduleRecId,
          schedule?.clientId?._id
        );
        if (!paymentLink) {
          return res.status(400).json({
            success: false,
            message: "Unable to create payment link.",
          });
        }

        const updateSchedule = await ScheduleDao.updateRecurrenceAmount(
          req.therapist._id,
          schedule._id,
          scheduleRecId,
          Number(amount),
          paymentLink.transactionId
        );
        if (!updateSchedule) {
          return res
            .status(400)
            .send({ success: false, message: "Unable to update schedule." });
        }

        let senderData = {
          email: req.therapist.email,
          name: req.therapist.name,
        };
        let receiverData = [
          {
            email: schedule?.clientId?.email,
            name: schedule.clientId?.name || "There",
          },
        ];

        const htmlTemplate = scheduleReminder({
          clientName: schedule?.clientId?.name || "There",
          therapistName: req.therapist.name,
          scheduleDate: reccuranceData.fromDate,
          meetingLink: reccuranceData.meetLink,
          // paymentLink: paymentLink.paymentLink,
          // payLater: true,
          timezone: schedule?.clientId?.defaultTimezone,
          amount: amount,
        });

        let subject = MailSubjectEnum.REMAINDER;

        const sentEmail = await Mailer2.sendMail(
          senderData,
          receiverData,
          subject,
          htmlTemplate
        );
        if (!sentEmail) {
          return res.status(400).json({ success: false, message: "Failed" });
        }
        const formattedDate = format(
          new Date(reccuranceData.fromDate),
          "dd-MM-yyyy"
        );
        const formattedTime = format(
          new Date(reccuranceData.fromDate),
          "HH:mm"
        );

        const params = {
          CLIENT_NAME: schedule?.clientId.name,
          THERAPIST_NAME: req?.therapist?.name,
          SESSION_DATE: formattedDate,
          SESSION_TIME: formattedTime,
        };

        await sendWhatsAppNotification(
          schedule?.clientId,
          "payment_reminder1",
          params
        );
      }

      // if(req.therapist.menus.paymentTracker){
      //     const paymentTracker = await PayTrackerService.getPaytrackerByScheduleIdAndScheduleRecId(schedule._id, reccuranceData._id);
      //     if(!paymentTracker){
      //         return res.status(400).send("Payment Tracker Not Found")
      //     }

      //     if (paymentTracker.status !=  PaymentTrackerStatusEnum.Still_Pending) {
      //         return res.status(400).send("Cannot update Payment Tracker as Status is "+ paymentTracker.status)
      //     }

      //     paymentTracker.amount = {
      //         currency: CurrencyEnum.INR,
      //         value: amount
      //     }
      //     await paymentTracker.save();

      //     const updateSchedule = await ScheduleDao.updateRecurrenceAmountForPayTracker(req.therapist._id, schedule._id, scheduleRecId, Number(amount), paymentTracker._id)
      //     if (!updateSchedule) {
      //         return res.status(500).send("Unable to update schedule for pay tracker.")
      //     }
      //     return res.status(200).send("Updated")
      // }

      res.send("Payment Link re-generated.");
    } catch (error) {
      next(error);
    }
  }

  static async resyncNew(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const resync = await GoogleCalendarService.resyncCalendarEvents(
        req.therapist._id
      );
      if (!resync) {
        return res.status(400).json({
          success: false,
          message: "Unable to resync calendar events.",
        });
      }

      if (resync.syncable.length == 0) {
        req.therapist.syncDate = new Date();
        await req.therapist.save();
      }

      res.send(resync);
      req.apiLog.response = JSON.stringify({ resync });
      await req.apiLog.save();
    } catch (e) {
      // The error handler middleware will handle conflicts with the conflicts array
      next(e);
    }
  }

  static async resyncV2(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const resync = await GoogleCalendarService.resyncCalendarEvents(
        req.params.therapistId,
        Number(req.query.maxResults) || 200
      );
      if (!resync) {
        return res.status(400).json({
          success: false,
          message: "Unable to resync calendar events.",
        });
      }

      if (resync.syncable.length == 0) {
        req.therapist.syncDate = new Date();
        await req.therapist.save();
      }

      res.send(resync);
    } catch (e) {
      // The error handler middleware will handle conflicts with the conflicts array
      next(e);
    }
  }

  // static async resyncCalendarEvents(req: express.Request, res: express.Response, next: express.NextFunction) {
  //     try {
  //         const therapistId = req.therapist._id;
  //         const therapist = req.therapist;
  //         const maxResults: any = req.query.maxResults;  // pass maxResults in query to fetch no. of events.

  //         const googleCalendarData = await GoogleCalendarService.findByTherapist(therapistId)

  //         if (!googleCalendarData) {
  //             return res.status(404).send("No Google Calender Data found.")
  //         }

  //         const oauth2Client = new google.auth.OAuth2(
  //             CONFIG.clientId,
  //             CONFIG.clientSecret,
  //             CONFIG.clientRedirectUrl
  //         );

  //         const tokens = {
  //             access_token: googleCalendarData.access_token,
  //             refresh_token: googleCalendarData.refresh_token  // you'll only get this once, so store it securely
  //         };

  //         // Assuming you're storing tokens in session after authentication
  //         if (tokens) {
  //             oauth2Client.setCredentials(tokens);

  //             const calendar = google.calendar({ version: 'v3', auth: oauth2Client });

  //             const calendarResponse = await calendar.events.list({
  //                 calendarId: 'primary',
  //                 timeMin: (new Date()).toISOString(),
  //                 maxResults: maxResults, // Change this value if you want to fetch more or fewer events
  //                 singleEvents: true,
  //                 orderBy: 'startTime',
  //             });

  //             const events: any = calendarResponse.data.items;
  //             if (events.length == 0) {
  //                 therapist.googleCalendarSynced = true;
  //                 await therapist.save()
  //             }
  //             if (events.length > 0) {
  //                 const iCalUIDs: any = events.map((event: any) => event.iCalUID);
  //                 const uniqueICalUIDs = Array.from(new Set(iCalUIDs));
  //                 let data = []
  //                 for (let iCalUid of uniqueICalUIDs) {
  //                     let event =  events.find((event: any) => event.iCalUID == iCalUid);
  //                     let eventIds =  events.filter((event: any) => event.iCalUID == iCalUid).map((event: any) => event.id)
  //                     let nonExistingEventIds = []
  //                     for (let eventId of eventIds) {
  //                         const isExistingEvent = await CalendarEventDao.getEventByEventId(eventId);
  //                         if (isExistingEvent) {
  //                             continue
  //                         }
  //                         nonExistingEventIds.push(eventId)
  //                     }
  //                     const eventOccurences = nonExistingEventIds.length;
  //                     event.eventOccurences = eventOccurences > 1 ? eventOccurences : 0
  //                     event.eventIds = nonExistingEventIds
  //                     if (event.eventIds.length > 0) {
  //                         data.push(event)
  //                     }
  //                 }
  //                 const syncable = data.filter((eve) => eve.attendees && eve.attendees.length > 1);
  //                 const not_syncable = data.filter((eve) => !eve.attendees);
  //                 res.send({ syncable, not_syncable })
  //             } else {
  //                 res.send('No upcoming events found.');
  //             }
  //         } else {
  //             throw new Error('Authentication tokens are not available');
  //         }

  //     } catch (error) {
  //         next(error);
  //     }
  // }

  static async markTransactionPaidOffline(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const recc_id = req.params.scheduleRecId;
      const therapistId = req.therapist._id;

      const schedule = await ScheduleDao.getScheduleByRecurrenceDateId(
        therapistId,
        recc_id
      );
      if (!schedule)
        return res.status(404).send(new Response({}, "No schedule found", 404));

      const reccurance = schedule.recurrenceDates.find(
        (recurrenceDate: any) => String(recurrenceDate._id) == String(recc_id)
      );
      if (!reccurance)
        return res
          .status(404)
          .send(new Response({}, "No recurrence found", 404));
      if (!reccurance.transactionId)
        return res
          .status(404)
          .send(new Response({}, "No transaction found", 404));

      const update_transaction = await TransactionService.markPaidOffline(
        reccurance.transactionId,
        therapistId
      );
      if (!update_transaction)
        return res
          .status(500)
          .send(new Response({}, "Unable to update transaction", 500));

      res
        .status(200)
        .send(
          new Response({}, "Transaction details updated successfully", 200)
        );
    } catch (e) {
      next(e);
    }
  }

  static async markTransactionPaidOfflineBy_id(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const transactionId = req.params._id;
      const therapistId = req.therapist._id;

      const update_transaction = await TransactionService.markPaidOffline(
        transactionId,
        therapistId
      );
      if (!update_transaction)
        return res
          .status(500)
          .send(new Response({}, "Unable to update transaction", 500));

      res
        .status(200)
        .send(
          new Response({}, "Transaction details updated successfully", 200)
        );
    } catch (e) {
      next(e);
    }
  }

  static async getTimeZones(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const allTimezones = momentTimezone.tz.names();
      const timezones = allTimezones.map((timezone) => {
        return {
          label: timezone,
          value: timezone,
        };
      });
      res.send({ timezones });
    } catch (err) {
      next(err);
    }
  }

  static async getScheduleClientData(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const scheduleRecId = req.params.id;
      const schedule = await ScheduleDao.getScheduleClientData(
        therapistId,
        scheduleRecId
      );
      if (!schedule) {
        return res
          .status(404)
          .json({ success: false, message: "No session found." });
      }
      res.send({ appointment: schedule });
    } catch (err) {
      next(err);
    }
  }

  static async checkClientPendingPayments(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const clientId = req.params.id;
      const therapistId = req.therapist._id;
      const currentTime = moment();
      let clientScheduleRecIdsCurrentTime: any = [];
      let pendingPayments = [];

      const clientSchedules =
        await ScheduleDao.getSchedulesByTherapistAndClientId(
          therapistId,
          clientId
        );
      if (!clientSchedules) {
        return res
          .status(404)
          .json({ success: false, message: "No session found." });
      }

      for (let clientSchedule of clientSchedules) {
        for (let recDate of clientSchedule.recurrenceDates) {
          if (moment(recDate.toDate) < currentTime) {
            clientScheduleRecIdsCurrentTime.push(recDate._id);
          }
        }
      }
      // if (clientScheduleRecIdsCurrentTime.length == 0) {
      //     return res.status(404).send("No session found before current time.")
      // }

      for (let scheduleRecId of clientScheduleRecIdsCurrentTime) {
        const isPaymentPending =
          await TransactionDao.getPendingTransactionByRecId(scheduleRecId);
        if (isPaymentPending) {
          pendingPayments.push(isPaymentPending);
        }
      }

      res.send({ isPending: pendingPayments.length > 0 ? true : false });
    } catch (err) {
      next(err);
    }
  }

  static async getS3Document(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const documentUrl = req.body.documentUrl;
      const fileName = documentUrl.split("/").pop();
      const file = await FileDownloadService.downloadObjectFile(fileName);
      if (!file) {
        return res.json({ success: false, message: "No file found." });
      }
      res.setHeader("Content-Type", "application/octet-stream");
      res.setHeader("Content-Disposition", `attachment; filename=${fileName}`);
      res.send(file.Body);
    } catch (err) {
      next(err);
    }
  }

  static async updateSyncDate(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const calendarId = req.therapist._id;
      const syncDate = new Date();
      const updateCalendarSyncDate = await TherapistService.updateSyncDate(
        calendarId,
        { syncDate: syncDate }
      );
      if (!updateCalendarSyncDate)
        return res
          .status(500)
          .send(new Response({}, "Unable to update calendar status", 500));

      res
        .status(200)
        .send(new Response({}, "Calendar sync date updated successfully", 200));
    } catch (err) {
      next(err);
    }
  }

  static async checkCalenderSyncStatus(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      let syncDate = req.therapist.syncDate
        ? moment(req.therapist.syncDate)
        : moment().subtract("1", "month");

      let currentDateMinus7Days = moment().subtract(7, "days");

      let isSyncAvailable: boolean = false;
      let entries = 0;

      if (syncDate.isAfter(currentDateMinus7Days)) {
        return res
          .status(200)
          .send(
            new Response(
              { syncable: isSyncAvailable },
              "Calendar status fetched successfully",
              200
            )
          );
      }

      const syncStatus = await GoogleCalendarService.resyncCalendarEvents(
        req.therapist._id,
        200
      );
      if (!syncStatus) {
        res
          .status(200)
          .send(
            new Response(
              { syncable: isSyncAvailable },
              "Calendar status fetched successfully",
              200
            )
          );
      }

      if (syncStatus.syncable.length > 0) {
        isSyncAvailable = true;
        entries = syncStatus.syncable.length;
      }

      res
        .status(200)
        .send(
          new Response(
            { syncable: isSyncAvailable, length: entries },
            "Calendar status fetched successfully",
            200
          )
        );
    } catch (err) {
      // The error handler middleware will handle conflicts with the conflicts array
      next(err);
    }
  }

  static async updateVpa(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const vpa = req.body.vpa;
      if (!vpa) {
        return res
          .status(400)
          .send({ success: false, message: "VPA is required." });
      }
      const updateVpa = await RazorpayService.createTestPayment(
        1,
        therapistId,
        vpa
      );
      if (!updateVpa) {
        return res
          .status(400)
          .send({ success: false, message: "Unable to update VPA." });
      }

      req.therapist.bankDetails.upiId = vpa;
      req.therapist.bankDetails.upiApprove = false;
      await req.therapist.save();
      res.send(new Response({}, "VPA updated successfully", 200));
    } catch (err) {
      next(err);
    }
  }

  static async rescheduleRecurringGoogleEvents(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const { recurrenceId, fromDate, toDate } = req.body;
      if (!recurrenceId || !fromDate || !toDate) {
        return throwError("Proper data is required", 400);
      }

      if (new Date(fromDate) > new Date(toDate)) {
        return res.status(400).json({
          success: false,
          message: "Enter a valid date",
        });
      }

      const newFromDate = moment(fromDate);
      const newToDate = moment(toDate);
      const duration = newToDate.diff(newFromDate, "minutes");

      if (duration <= 0) {
        return res.status(400).json({
          success: false,
          message: "Select a valid Time for Appointment Duration",
        });
      }

      // First, check if there's already an event in Google Calendar at the new date/time
      // This check happens before any other processing to prevent any changes if there's a conflict
      const existingGoogleEvents = await GoogleCalendarService.eventByDate(
        req.therapist._id,
        200,
        new Date(fromDate),
        new Date(toDate)
      );

      // Check all dates in the series for conflicts
      const initialConflicts: { startTime: string; endTime: string }[] = [];

      // First, check the initial date
      if (existingGoogleEvents && existingGoogleEvents.length > 0) {
        const formattedDate = moment(fromDate).format("YYYY-MM-DD HH:mm");
        // Add each conflicting event to our conflicts array
        for (const event of existingGoogleEvents) {
          initialConflicts.push({
            startTime: event.start.dateTime,
            endTime: event.end.dateTime,
          });
        }
      }

      // Get the schedule first to access its recurrence pattern
      const scheduleData = await ScheduleService.getSchedulesRecurrenceId(
        req.therapist._id,
        recurrenceId
      );

      // Generate all future dates based on the recurrence pattern
      const futureDates = [];

      // Get the recurrence pattern from the schedule
      const recurrencePattern = scheduleData?.recurrence || "Weekly";

      // If it's a recurring schedule, generate future dates
      if (recurrencePattern && recurrencePattern !== "Does Not Repeat") {
        // Determine how many future dates to check based on recurrence pattern
        let count = 12; // Default to 12 occurrences
        let interval = 7; // Default to weekly

        if (recurrencePattern.includes("Every Day")) {
          interval = 1; // Daily
        } else if (recurrencePattern.includes("Two Weeks")) {
          interval = 14; // Bi-weekly
        }

        // Generate future dates
        for (let i = 1; i < count; i++) {
          const nextDate = moment(fromDate).add(i * interval, "days");
          const nextEndDate = moment(toDate).add(i * interval, "days");

          futureDates.push({
            fromDate: nextDate.toDate(),
            toDate: nextEndDate.toDate(),
          });
        }

        // Check each future date for conflicts
        for (const date of futureDates) {
          const futureEvents = await GoogleCalendarService.eventByDate(
            req.therapist._id,
            200,
            date.fromDate,
            date.toDate
          );

          if (futureEvents && futureEvents.length > 0) {
            // Add each conflicting event to our conflicts array
            for (const event of futureEvents) {
              initialConflicts.push({
                startTime: event.start.dateTime,
                endTime: event.end.dateTime,
              });
            }
          }
        }
      }

      // If we found any conflicts, return them
      if (initialConflicts.length > 0) {
        // Create a user-friendly array of conflict dates
        const conflictDates = initialConflicts.map((conflict) => {
          const startTime = moment(conflict.startTime).format(
            "YYYY-MM-DD HH:mm"
          );
          const endTime = moment(conflict.endTime).format("HH:mm");
          return `${startTime} - ${endTime}`;
        });

        // Format the first conflict for the message
        const firstConflict = initialConflicts[0];
        const firstConflictDate = moment(firstConflict.startTime).format(
          "YYYY-MM-DD HH:mm"
        );

        // Create message that matches the format used in the sync process
        let message = "";
        if (initialConflicts.length === 1) {
          message = `A conflict was found. You have a session on ${firstConflictDate}.`;
        } else {
          message = `A conflict was found. You have ${initialConflicts.length} existing sessions in your calendar.`;
          message += ` For example, you have a session on ${firstConflictDate}.`;
        }

        // Return a 200 response with the conflicts array
        return res.status(200).json({
          success: false,
          message: message,
          conflicts: initialConflicts,
          totalConflicts: initialConflicts.length,
          hasConflicts: true,
          conflictDates: conflictDates,
        });
      }

      const schedule = await ScheduleService.getSchedulesRecurrenceId(
        req.therapist._id,
        recurrenceId
      );

      if (!schedule) {
        return throwError("No Schedule found", 404);
      }

      const currentDate = moment();
      const datesToUpdate = schedule.recurrenceDates.filter((rec: any) =>
        moment(rec.fromDate).isAfter(currentDate)
      );

      const allTherapistSchedule =
        await ScheduleService.getAllScheduleByTherapist(req.therapist?._id);

      const allRecurrenceWithoutCurrentSchedule: any =
        allTherapistSchedule.filter(
          (rec: any) => rec._id.toString() !== schedule._id.toString()
        );

      const allRecurrence: { fromDate: Date; toDate: Date; _id?: any }[] = [];
      for (let schedule of allRecurrenceWithoutCurrentSchedule) {
        if (
          schedule.recurrenceDates &&
          Array.isArray(schedule.recurrenceDates)
        ) {
          const extractedDates = schedule.recurrenceDates.map(
            (recurrence: any) => ({
              fromDate: recurrence.fromDate,
              toDate: recurrence.toDate,
              _id: recurrence._id, // Include the _id property for conflict detection
            })
          );
          allRecurrence.push(...extractedDates);
        }
      }
      allRecurrence.sort(
        (a, b) => +new Date(a.fromDate) - +new Date(b.fromDate)
      );

      const updatedDates = datesToUpdate.map((recurrence: any) => {
        const updatedFromDate = combineDateWithTime(
          recurrence.fromDate,
          fromDate
        );
        const updatedToDate = combineDateWithTime(recurrence.toDate, toDate);
        return {
          fromDate: updatedFromDate,
          toDate: updatedToDate,
          _id: recurrence._id, // Include the _id property for conflict detection
        };
      });

      const dbConflicts: { startTime: string; endTime: string }[] = [];

      // Check each updated date against all existing recurrence dates
      for (const updatedDate of updatedDates) {
        // Find overlapping dates in the database
        const overlappingDates = allRecurrence.filter((existingDate) => {
          // Skip the sessions being rescheduled
          if (
            existingDate._id &&
            datesToUpdate.some(
              (dateToUpdate) =>
                dateToUpdate._id &&
                String(dateToUpdate._id) === String(existingDate._id)
            )
          ) {
            return false;
          }

          const isOverlap =
            moment(updatedDate.fromDate).isBefore(
              moment(existingDate.toDate)
            ) &&
            moment(updatedDate.toDate).isAfter(moment(existingDate.fromDate));

          return isOverlap;
        });

        // Format conflicts in the same way as Google Calendar conflicts
        for (const overlappingDate of overlappingDates) {
          dbConflicts.push({
            startTime: moment(overlappingDate.fromDate).toISOString(),
            endTime: moment(overlappingDate.toDate).toISOString(),
          });
        }
      }

      // If we found conflicts in the database, return a 200 response with the conflicts array
      if (dbConflicts.length > 0) {
        // Format the first conflict for the message
        const firstConflict = dbConflicts[0];
        const conflictDate = moment(firstConflict.startTime).format(
          "YYYY-MM-DD h:mm A"
        );

        let errorMessage = "";

        if (dbConflicts.length === 1) {
          errorMessage = `A conflict was found. You have a session in your database on ${conflictDate}.`;
        } else {
          errorMessage = `Conflicts were found. You have ${dbConflicts.length} existing sessions in your database that overlap with the new session.`;
          errorMessage += ` For example, you have a session on ${conflictDate}.`;
        }

        // Create a user-friendly array of conflict dates
        const conflictDates = dbConflicts.map((conflict) => {
          const startTime = moment(conflict.startTime).format(
            "YYYY-MM-DD HH:mm"
          );
          const endTime = moment(conflict.endTime).format("HH:mm");
          return `${startTime} - ${endTime}`;
        });

        return res.status(200).json({
          success: false,
          message: errorMessage,
          conflicts: dbConflicts,
          totalConflicts: dbConflicts.length,
          hasConflicts: true,
          conflictDates: conflictDates,
        });
      }

      // Check if events exist in Google Calendar
      const oldCalendarUpdatePromises = datesToUpdate.map((recurrence) =>
        GoogleCalendarService.eventByDate(
          req.therapist._id,
          200,
          recurrence.fromDate,
          recurrence.toDate
        )
      );

      const oldEventChecks = await Promise.all(oldCalendarUpdatePromises);

      const calendarUpdatePromises = updatedDates.map((recurrence) =>
        GoogleCalendarService.eventByDate(
          req.therapist._id,
          200,
          recurrence.fromDate,
          recurrence.toDate
        )
      );

      const eventChecks = await Promise.all(calendarUpdatePromises);

      // Initialize with any initial conflicts we found
      const conflicts: { startTime: string; endTime: string }[] = [
        ...initialConflicts,
      ];

      for (let i = 0; i < eventChecks.length; i++) {
        const newEvents = eventChecks[i];
        const oldEvents = oldEventChecks[i];

        // Find conflicting events (events that don't match old events)
        const conflictingEvents = newEvents.filter((newEvent: any) => {
          // Check if this is one of the events we're rescheduling
          const isExistingEvent = oldEvents.some(
            (oldEvent: any) =>
              newEvent.id === oldEvent.id &&
              newEvent.start.dateTime === oldEvent.start.dateTime &&
              newEvent.end.dateTime === oldEvent.end.dateTime &&
              newEvent.status === oldEvent.status
          );

          // If it's an existing event, it's not a conflict
          const isConflict = !isExistingEvent;
          return isConflict;
        });

        // Add each conflicting event to our conflicts array
        for (const event of conflictingEvents) {
          conflicts.push({
            startTime: event.start.dateTime,
            endTime: event.end.dateTime,
          });
        }
      }

      // If we found conflicts in Google Calendar, return a 200 response with the conflicts array
      if (conflicts.length > 0) {
        // Format the first conflict for the message
        const firstConflict = conflicts[0];
        const conflictDate = moment(firstConflict.startTime).format(
          "YYYY-MM-DD h:mm A"
        );

        let errorMessage = "";

        if (conflicts.length === 1) {
          errorMessage = `A conflict was found. You have a session in your Google Calendar on ${conflictDate}.`;
        } else {
          errorMessage = `Conflicts were found. You have ${conflicts.length} existing sessions in your Google Calendar that overlap with the new session.`;
          errorMessage += ` For example, you have a session on ${conflictDate}.`;
        }

        // Create a user-friendly array of conflict dates
        const conflictDates = conflicts.map((conflict) => {
          const startTime = moment(conflict.startTime).format(
            "YYYY-MM-DD HH:mm"
          );
          const endTime = moment(conflict.endTime).format("HH:mm");
          return `${startTime} - ${endTime}`;
        });

        return res.status(200).json({
          success: false,
          message: errorMessage,
          conflicts: conflicts,
          totalConflicts: conflicts.length,
          hasConflicts: true,
          conflictDates: conflictDates,
        });
      }

      // Find the selected session (the one being rescheduled)
      const selectedSession = schedule.recurrenceDates.find(
        (rec: any) => rec._id.toString() === recurrenceId
      );

      if (!selectedSession) {
        return res.status(400).json({
          success: false,
          message: "Selected session not found",
        });
      }

      // Calculate the date difference between the selected session's original date and the new date
      const selectedOriginalDate = moment(selectedSession.fromDate);
      const selectedNewDate = moment(fromDate);

      // Calculate the date difference for reference
      selectedNewDate.diff(selectedOriginalDate, "days");

      // Get all sessions and sort them by date
      let allSessions = [...schedule.recurrenceDates];
      allSessions.sort((a, b) => moment(a.fromDate).diff(moment(b.fromDate)));

      // Find the first upcoming session (the first session with a date greater than or equal to the current date)
      // We use greater than or equal to ensure we include sessions that are very close to the current date
      const upcomingSessions = allSessions.filter((rec: any, index) => {
        // Always include the first session in the series (after sorting)
        if (index === 0) {
          return true;
        }
        // For other sessions, only include if they're after the current date
        return moment(rec.fromDate).isAfter(currentDate);
      });

      // Get the first upcoming session
      const firstUpcomingSession =
        upcomingSessions.length > 0 ? upcomingSessions[0] : null;

      if (!firstUpcomingSession) {
        return res.status(400).json({
          success: false,
          message: "No upcoming sessions found to reschedule",
        });
      }

      // Always use the first upcoming session's date for rescheduling the entire slot
      const firstRecurrenceDate = firstUpcomingSession;
      const originalFirstDate = moment(firstRecurrenceDate.fromDate);

      // Create a new date using the first upcoming session's date but with the new time
      moment(originalFirstDate)
        .hour(moment(fromDate).hour())
        .minute(moment(fromDate).minute())
        .second(moment(fromDate).second());

      // Collect all calendar event IDs for the series
      const googleEventUpdates: {
        therapistId: string;
        eventId: string;
        newDates: { fromDate: Date; toDate: Date };
      }[] = [];

      // Get all sessions and sort them by date
      allSessions = [...schedule.recurrenceDates];
      allSessions.sort((a, b) => moment(a.fromDate).diff(moment(b.fromDate)));

      // Find the index of the selected session in the sorted array
      const selectedSessionIndex = allSessions.findIndex(
        (rec) => rec._id.toString() === recurrenceId
      );

      // Create a map of session IDs that should be preserved (not rescheduled)
      const sessionsToPreserve = new Map();
      for (let i = 0; i < selectedSessionIndex; i++) {
        const session = allSessions[i];
        sessionsToPreserve.set(session._id.toString(), true);
      }

      // Find all events in the series that are in the future and after the selected session
      for (const rec of schedule.recurrenceDates) {
        // Skip sessions that should be preserved (past sessions before the selected session)
        if (sessionsToPreserve.has(rec._id.toString())) {
          continue;
        }

        // When rescheduling an entire slot, we want to include previously individually rescheduled sessions
        // This ensures that if a session was previously rescheduled (e.g., 14th to 16th), it will be included
        // in the new series flow (e.g., 10th, 17th, 24th, 31st) and not cause duplicates

        if (moment(rec.fromDate).isAfter(currentDate) && rec.calenderEventId) {
          // Calculate the new dates for this recurrence
          const originalFromDate = moment(rec.fromDate);
          const originalToDate = moment(rec.toDate);

          // For previously rescheduled single sessions, we need to align them with the new series pattern
          // This means calculating the correct date based on the index in the series

          // Find all sessions that will be processed
          const sessionsToProcess = allSessions.filter(
            (session) => !sessionsToPreserve.has(session._id.toString())
          );

          // Sort them by date
          sessionsToProcess.sort((a, b) =>
            moment(a.fromDate).diff(moment(b.fromDate))
          );

          // Find the index of this session in the sorted array
          const sessionIndex = sessionsToProcess.findIndex(
            (session) => session._id.toString() === rec._id.toString()
          );

          // Get the first session's new date (the reference date for the series)
          const firstSessionNewDate = moment(fromDate);

          // Calculate the date for this session based on its position in the series
          // This ensures all sessions follow a consistent pattern regardless of previous rescheduling
          let shiftedDate;

          // Get the recurrence pattern from the schedule
          const recurrencePattern =
            schedule.recurrence || "Weekly on Wednesday";

          if (sessionIndex === 0) {
            // This is the first session in the series, use the new date directly
            shiftedDate = firstSessionNewDate.clone();
          } else {
            // This is a subsequent session, calculate its date based on the pattern
            shiftedDate = firstSessionNewDate.clone();

            if (recurrencePattern.includes("Daily")) {
              // Daily pattern - add days equal to the index
              shiftedDate.add(sessionIndex, "days");
            } else if (recurrencePattern.includes("Weekly")) {
              // Weekly pattern - add weeks equal to the index
              shiftedDate.add(sessionIndex, "weeks");
            } else if (recurrencePattern.includes("Biweekly")) {
              // Biweekly pattern - add 2 weeks for each index
              shiftedDate.add(sessionIndex * 2, "weeks");
            } else {
              // Default to weekly if pattern is not recognized
              shiftedDate.add(sessionIndex, "weeks");
            }
          }

          // Apply the time from the request
          const updatedFromDate = moment(shiftedDate)
            .hour(newFromDate.hour())
            .minute(newFromDate.minute())
            .second(newFromDate.second())
            .millisecond(newFromDate.millisecond())
            .toDate();

          // Calculate the new end time by adding the original duration to the new start time
          // This ensures we maintain the same session length
          const originalDuration = moment.duration(
            originalToDate.diff(originalFromDate)
          );

          const updatedToDate = moment(updatedFromDate)
            .add(originalDuration)
            .toDate();

          // Skip creating sessions with dates before the current date
          // But make an exception for the first session in the series
          // This ensures the first session is always created even if it's close to the current date

          // Always create the first session, regardless of its date
          if (moment(updatedFromDate).isBefore(currentDate)) {
            continue;
          }

          // Update the dates in the schedule object
          rec.fromDate = updatedFromDate;
          rec.toDate = updatedToDate;
          rec.status = ScheduleStatus.RESCHEDULED;

          // Add to the list of events to update
          if (rec.calenderEventId) {
            googleEventUpdates.push({
              therapistId: req.therapist._id,
              eventId: rec.calenderEventId.toString(),
              newDates: { fromDate: updatedFromDate, toDate: updatedToDate },
            });
          }
        }
      }

      // Use our new batchUpdateEvents method to update all events in the series
      const batchResult = await TherapistController.batchUpdateEvents(
        googleEventUpdates
      );

      // If there are conflicts from the batch update, return them to the client
      if (
        batchResult &&
        batchResult.conflicts &&
        batchResult.conflicts.length > 0
      ) {
        // Create a user-friendly array of conflict dates
        const conflictDates = batchResult.conflicts.map((conflict) => {
          const startTime = moment(conflict.startTime).format(
            "YYYY-MM-DD HH:mm"
          );
          const endTime = moment(conflict.endTime).format("HH:mm");
          return `${startTime} - ${endTime}`;
        });

        return res.status(200).json({
          success: false,
          message:
            batchResult.message ||
            "Conflicts found when updating Google Calendar events",
          conflicts: batchResult.conflicts,
          totalConflicts: batchResult.conflicts.length,
          hasConflicts: true,
          conflictDates: conflictDates,
        });
      }
      schedule.durationOfSchedule = duration;
      await schedule.save();

      const client: any = await ClientModel.findOne({
        _id: schedule.clientId,
      });

      // Use the first upcoming session's date for the email notification
      const firstUpcomingSessionDate = moment(firstUpcomingSession.fromDate);

      // Apply the new time to the first upcoming session's date
      const emailNotificationDate = moment(firstUpcomingSessionDate)
        .hour(newFromDate.hour())
        .minute(newFromDate.minute())
        .second(newFromDate.second());

      const sessionDate = emailNotificationDate
        .tz(client?.defaultTimezone || "Asia/Kolkata")
        .format("Do MMM, YYYY");

      const sessionTime = emailNotificationDate
        .tz(client?.defaultTimezone || "Asia/Kolkata")
        .format("hh:mm A");

      const day = emailNotificationDate
        .tz(client?.defaultTimezone || "Asia/Kolkata")
        .format("dddd");

      let htmlTemplate = rescheduleSlot(
        sessionDate,
        sessionTime,
        schedule.recurrenceDates[0]?.amount || 0,
        req.therapist?.name,
        client?.name || "There",
        schedule.recurrenceDates[0]?.meetLink || "",
        schedule?.recurrence || "Does Not Repeat",
        day
      );

      let senderData = {
        email: req.therapist.email,
        name: req.therapist.name,
      };
      const receiverData = [
        {
          email: client?.email || "",
          name: client?.name || "There",
        },
      ];
      let subject = MailSubjectEnum.APPOINTMENT_RESCHEDULE_SLOT;
      // Send email notification
      await Mailer2.sendMail(senderData, receiverData, subject, htmlTemplate);
      const formattedDate = sessionDate;
      const formattedTime = sessionTime;

      let selectedDay;
      let sessionFrequency;
      if (schedule.recurrence) {
        let words = (schedule?.recurrence).split(" ");
        selectedDay = words[words.length - 1];
        words.pop();
        sessionFrequency = words.join(" ");
      }

      const params = {
        CLIENT_NAME: client.name,
        THERAPIST_NAME: req?.therapist?.name,
        SESSION_DATE: formattedDate,
        SESSION_TIME: formattedTime,
        DAY_NAME: selectedDay || "scheduled",
        SESSION_FREQUENCY: sessionFrequency || "the specified day",
        MEETLINK: schedule.recurrenceDates[0]?.meetLink || "",
      };

      await sendWhatsAppNotification(
        client,
        "reschedule_session_slot1",
        params
      );

      // Create a copy of the arrays to avoid reference issues
      const dbConflictsCopy = [...(dbConflicts || [])];
      const conflictsCopy = [...(conflicts || [])];

      // No need to add test conflicts anymore

      // Combine database and Google Calendar conflicts
      const allConflicts = [...dbConflictsCopy, ...conflictsCopy];

      // Send the response with the schedule and conflicts
      const response = {
        schedule,
        hasConflicts: allConflicts.length > 0,
        conflicts: allConflicts,
        totalConflicts: allConflicts.length,
        dbConflicts: dbConflictsCopy,
        gcalConflicts: conflictsCopy,
      };

      res.status(200).send(response);
    } catch (error: any) {
      next(error);
    }
  }

  // static async batchUpdateEvents(
  //   events: {
  //     therapistId: string;
  //     eventId: string;
  //     newDates: { fromDate: Date; toDate: Date };
  //   }[]
  // ): Promise<void> {
  //   const BATCH_SIZE = 5;
  //   const MAX_RETRIES = 3;

  //   for (let i = 0; i < events.length; i += BATCH_SIZE) {
  //     const batch = events.slice(i, i + BATCH_SIZE);
  //     let attempts = 0;
  //     let success = false;

  //     while (attempts < MAX_RETRIES && !success) {
  //       try {
  //         await Promise.all(
  //           batch.map((update) =>
  //             GoogleCalendarService.updateEventTime(
  //               update.therapistId,
  //               update.eventId.toString(),
  //               update.newDates
  //             )
  //           )
  //         );
  //         success = true;
  //       } catch (error: any) {
  //         if (
  //           error.code === 403 &&
  //           error.errors?.some((e: any) => e.reason === "rateLimitExceeded")
  //         ) {
  //           // Exponential backoff
  //           const waitTime = Math.pow(2, attempts) * 1000;
  //           console.log(`Rate limit exceeded, retrying in ${waitTime}ms...`);
  //           await new Promise((resolve) => setTimeout(resolve, waitTime));
  //         } else {
  //           throw error;
  //         }
  //       }
  //       attempts++;
  //     }
  //   }
  // }

  static async batchUpdateEvents(
    events: {
      therapistId: string;
      eventId: string;
      newDates: { fromDate: Date; toDate: Date };
    }[]
  ): Promise<{
    conflicts?: { startTime: string; endTime: string }[];
    success?: boolean;
    message?: string;
  }> {
    try {
      // Group events by scheduleId to handle all events in a series together
      const eventsBySchedule: { [key: string]: any[] } = {};

      // First, group all events by scheduleId
      for (const eventData of events) {
        const { eventId } = eventData; // Only destructure what we need

        // Get the calendar event from the database using findby_id
        const calendarEventData = await CalendarEventDao.findby_id(eventId);
        if (!calendarEventData?.id || !calendarEventData.scheduleId) {
          continue;
        }

        const scheduleId = calendarEventData.scheduleId.toString();
        if (!eventsBySchedule[scheduleId]) {
          eventsBySchedule[scheduleId] = [];
        }

        eventsBySchedule[scheduleId].push({
          ...eventData,
          calendarEventData,
        });
      }

      // Now process each schedule's events together
      for (const scheduleId of Object.keys(eventsBySchedule)) {
        const scheduleEvents = eventsBySchedule[scheduleId];
        if (scheduleEvents.length === 0) continue;

        // Use the first event to get common data
        const firstEvent = scheduleEvents[0];
        const { therapistId, newDates } = firstEvent;
        const newStart = newDates.fromDate;

        // First, check if there's already an event in Google Calendar at the new date/time
        // This check happens before any other processing to prevent any changes if there's a conflict
        const existingGoogleEvents = await GoogleCalendarService.eventByDate(
          therapistId,
          200,
          newDates.fromDate,
          newDates.toDate
        );

        // If there are any events at this time, collect them as conflicts and return
        if (existingGoogleEvents && existingGoogleEvents.length > 0) {
          // Collect all conflicts
          const batchConflicts = existingGoogleEvents.map((event: any) => ({
            startTime: event.start.dateTime,
            endTime: event.end.dateTime,
          }));

          // Return the conflicts to be handled by the caller
          return {
            success: false,
            message: "Conflicts found in Google Calendar",
            conflicts: batchConflicts,
          };
        }

        // Get Google Calendar credentials
        const googleCalendarData = await GoogleCalendarService.findByTherapist(
          therapistId
        );
        if (!googleCalendarData) {
          continue;
        }

        // Get the schedule for this series
        const schedule = await ScheduleDao.getById(therapistId, scheduleId);

        if (!schedule) {
          continue;
        }

        // Get client details
        const client = await ClientModel.findById(schedule.clientId);
        if (!client) {
          continue;
        }

        // Get the current date to determine which events to delete
        const currentDate = moment();

        // Find all sessions in the series
        const allRecurrenceDates = [...schedule.recurrenceDates];

        // Sort recurrence dates by date to ensure proper order
        allRecurrenceDates.sort((a, b) =>
          moment(a.fromDate).diff(moment(b.fromDate))
        );

        // Find the first upcoming session (the first session with a date greater than the current date)
        const upcomingSessions = allRecurrenceDates.filter((rec) =>
          moment(rec.fromDate).isAfter(currentDate)
        );

        // Sort upcoming sessions by date to ensure we get the earliest one
        upcomingSessions.sort((a, b) =>
          moment(a.fromDate).diff(moment(b.fromDate))
        );

        // Get the first upcoming session
        const firstUpcomingSession =
          upcomingSessions.length > 0 ? upcomingSessions[0] : null;

        if (!firstUpcomingSession) {
          continue; // Skip to the next schedule
        }

        // Get the event ID from the request to find the selected session
        const selectedEventId = scheduleEvents[0].eventId;

        // Find the calendar event for the selected session
        const selectedCalendarEvent = await CalendarEventDao.findby_id(
          selectedEventId
        );

        if (!selectedCalendarEvent) {
          continue;
        }

        // Find the selected session in the recurrence dates
        const selectedSession = schedule.recurrenceDates.find(
          (rec) =>
            rec.calenderEventId &&
            rec.calenderEventId.toString() === selectedEventId
        );

        if (!selectedSession) {
          continue;
        }

        // Calculate the date difference between the selected session's original date and the new date
        const selectedOriginalDate = moment(selectedSession.fromDate);
        const selectedNewDate = moment(newStart);

        // Calculate the date difference for reference
        selectedNewDate.diff(selectedOriginalDate, "days");

        // Set up OAuth client for Google Calendar API
        const oauth2Client = new google.auth.OAuth2(
          CONFIG.clientId,
          CONFIG.clientSecret,
          CONFIG.clientRedirectUrl
        );
        oauth2Client.setCredentials({
          access_token: googleCalendarData.access_token,
          refresh_token: googleCalendarData.refresh_token,
        });

        const calendar = google.calendar({ version: "v3", auth: oauth2Client });

        // Collect event IDs from the database for this schedule, but only for future dates
        const eventIdsToDelete: string[] = [];
        const processedEventIds = new Set<string>(); // Track processed events to avoid duplicates

        // Include all sessions in the series for rescheduling
        const sessionsToReschedule = [...allRecurrenceDates];

        // Sort sessions by date to ensure proper order (earliest first)
        sessionsToReschedule.sort((a, b) =>
          moment(a.fromDate).diff(moment(b.fromDate))
        );

        // Find the index of the selected session in the sorted array
        const selectedSessionIndex = sessionsToReschedule.findIndex(
          (rec) => rec._id.toString() === selectedSession._id.toString()
        );

        // Create a map of session IDs that should be preserved (not rescheduled)
        const sessionsToPreserve = new Map();
        for (let i = 0; i < selectedSessionIndex; i++) {
          const session = sessionsToReschedule[i];
          sessionsToPreserve.set(session._id.toString(), true);
        }
        const futureRecurrenceDates = sessionsToReschedule.filter(
          (_, index) => {
            // Include only the selected session and sessions after it
            return index >= selectedSessionIndex;
          }
        );

        for (const recurrenceDate of futureRecurrenceDates) {
          if (recurrenceDate.calenderEventId) {
            const calendarEvent = await CalendarEventDao.findby_id(
              recurrenceDate.calenderEventId
            );
            if (calendarEvent && calendarEvent.id) {
              // Only add if not already processed
              if (!processedEventIds.has(calendarEvent.id)) {
                eventIdsToDelete.push(calendarEvent.id);
                processedEventIds.add(calendarEvent.id);
              }
            }
          }
        }

        // Delete each future event from Google Calendar
        for (const eventId of eventIdsToDelete) {
          try {
            await calendar.events.delete({
              calendarId: "primary",
              eventId: eventId,
            });
            console.log(`Successfully deleted event: ${eventId}`);
          } catch (error: any) {
            if (error.response?.status === 404) {
              console.warn(`Event ${eventId} not found in Google Calendar (already deleted)`);
            } else if (error.response?.status === 400) {
              console.warn(`Bad request when deleting event ${eventId}: ${error.message}`);
            } else {
              console.error(`ERROR: Failed to delete event ${eventId} from Google Calendar:`, error.message);
            }
            // Continue with the next event even if deletion fails
          }
        }

        // Step 2: Remove calendar event IDs from the database only for the selected session and future sessions
        for (const recurrenceDate of futureRecurrenceDates) {
          if (recurrenceDate.calenderEventId) {
            await ScheduleDao.removeEventFromRecurrence(
              therapistId,
              recurrenceDate._id
            );
          }
        }

        // Step 3: Update dates in the database only for the selected session and future sessions
        for (let i = 0; i < futureRecurrenceDates.length; i++) {
          const recurrenceDate = futureRecurrenceDates[i];

          // Skip sessions that should be preserved (past sessions)
          if (sessionsToPreserve.has(recurrenceDate._id.toString())) {
            continue;
          }

          // Calculate the new dates for this recurrence
          const originalFromDate = moment(recurrenceDate.fromDate);
          const originalToDate = moment(recurrenceDate.toDate);

          // Get the original duration between start and end time
          const originalDuration = moment.duration(
            originalToDate.diff(originalFromDate)
          );

          // Find the index of this session in the futureRecurrenceDates array
          const sessionIndex = futureRecurrenceDates.findIndex(
            (rec) => rec._id.toString() === recurrenceDate._id.toString()
          );

          // Get the time components from the new start time
          const newStartHour = moment(newStart).hour();
          const newStartMinute = moment(newStart).minute();
          const newStartSecond = moment(newStart).second();

          // Get the first session's new date (the reference date for the series)
          const firstSessionNewDate = moment(newStart);

          // Calculate the date for this session based on its position in the series
          // This ensures all sessions follow a consistent pattern regardless of previous rescheduling
          let shiftedDate;

          // Get the recurrence pattern from the schedule
          const recurrencePattern =
            schedule.recurrence || "Weekly on Wednesday";

          if (sessionIndex === 0) {
            // This is the first session in the series, use the new date directly
            shiftedDate = firstSessionNewDate.clone();
          } else {
            // This is a subsequent session, calculate its date based on the pattern
            shiftedDate = firstSessionNewDate.clone();

            if (recurrencePattern.includes("Daily")) {
              // Daily pattern - add days equal to the index
              shiftedDate.add(sessionIndex, "days");
            } else if (recurrencePattern.includes("Weekly")) {
              // Weekly pattern - add weeks equal to the index
              shiftedDate.add(sessionIndex, "weeks");
            } else if (recurrencePattern.includes("Biweekly")) {
              // Biweekly pattern - add 2 weeks for each index
              shiftedDate.add(sessionIndex * 2, "weeks");
            } else {
              // Default to weekly if pattern is not recognized
              shiftedDate.add(sessionIndex, "weeks");
            }
          }

          // Apply the time from the new start time
          const updatedFromDate = moment(shiftedDate)
            .hour(newStartHour)
            .minute(newStartMinute)
            .second(newStartSecond)
            .millisecond(0) // Reset milliseconds to avoid precision issues
            .toDate();

          // Skip creating sessions with dates before the current date
          // But make an exception for the first session in the series
          // This ensures the first session is always created even if it's close to the current date

          // Always create the first session, regardless of its date
          if (moment(updatedFromDate).isBefore(currentDate)) {
            continue;
          }

          // Calculate the new end time by adding the original duration to the new start time
          // This ensures we maintain the same session length
          const updatedToDate = moment(updatedFromDate)
            .add(originalDuration)
            .toDate();

          // Update the dates in the database
          recurrenceDate.fromDate = updatedFromDate;
          recurrenceDate.toDate = updatedToDate;
          recurrenceDate.status = ScheduleStatus.RESCHEDULED;

          // Mark this as part of a batch reschedule to distinguish from individually rescheduled sessions
          if (!recurrenceDate.other) {
            recurrenceDate.other = {
              isRefunded: false,
              isRescheduled: true,
            };
          } else {
            recurrenceDate.other.isRescheduled = true;
          }
        }

        // Step 4: Create new events only for the selected session and future sessions
        // Filter out sessions that should be preserved (past sessions)
        const sessionsToCreateEvents = futureRecurrenceDates.filter(
          (rec) => !sessionsToPreserve.has(rec._id.toString())
        );

        // Log the sessions that will be updated in Google Calendar

        // Get all emails for the client
        const emails = [client.email];
        // Check if schedule has additional emails
        if (schedule.additionalEmails && schedule.additionalEmails.length > 0) {
          emails.push(...schedule.additionalEmails);
        }

        const addEventPayload = {
          emails: emails,
          summary: schedule.summary || "Therapy Session",
          location: schedule.location || "online",
          description: schedule.description || "",
        };

        // Check if this is a recurring schedule
        const isRecurring =
          schedule.recurrence && schedule.recurrence !== "Does Not Repeat";

        if (schedule.location === "online") {
          // Make sure we have sessions to reschedule
          if (sessionsToCreateEvents.length === 0) {
            continue; // Skip to the next schedule
          }

          // Get the date range for all sessions to create events for
          const allDates = sessionsToCreateEvents.map((rec) => ({
            fromDate: rec.fromDate,
            toDate: rec.toDate,
          }));

          // Also check for events one day after each recurrence date to catch potential duplicates
          const nextDayDates = sessionsToCreateEvents.map((rec) => {
            const nextDayFrom = moment(rec.fromDate).add(1, "days").toDate();
            const nextDayTo = moment(rec.toDate).add(1, "days").toDate();
            return {
              fromDate: nextDayFrom,
              toDate: nextDayTo,
            };
          });

          // Also check for events one day before each recurrence date to catch potential duplicates
          const prevDayDates = sessionsToCreateEvents.map((rec) => {
            const prevDayFrom = moment(rec.fromDate)
              .subtract(1, "days")
              .toDate();
            const prevDayTo = moment(rec.toDate).subtract(1, "days").toDate();
            return {
              fromDate: prevDayFrom,
              toDate: prevDayTo,
            };
          });

          // Combine all sets of dates to check
          const datesToCheck = [...allDates, ...nextDayDates, ...prevDayDates];

          const existingEvents =
            await GoogleCalendarService.findEventsByTherapistAndDates(
              therapistId,
              datesToCheck
            );

          if (existingEvents && existingEvents.length > 0) {
            // Delete any duplicate events
            for (const event of existingEvents) {
              try {
                if (event.id) {
                  await calendar.events.delete({
                    calendarId: "primary",
                    eventId: event.id.toString(),
                  });
                  console.log(`Successfully deleted duplicate event: ${event.id}`);
                }
              } catch (error: any) {
                if (error.response?.status === 404) {
                  console.warn(`Duplicate event ${event.id} not found in Google Calendar (already deleted)`);
                } else if (error.response?.status === 400) {
                  console.warn(`Bad request when deleting duplicate event ${event.id}: ${error.message}`);
                } else {
                  console.error(`ERROR: Failed to delete duplicate event ${event.id}:`, error.message);
                }
              }
            }
          }

          if (isRecurring && sessionsToCreateEvents.length > 1) {
            // For recurring schedules, create a single recurring event for the selected session and future sessions
            // Use the first future date as the start of the recurring series
            const firstFutureDate = sessionsToCreateEvents[0];

            // Log the first future date that will be used for creating recurring series
            // Create a recurrence rule based on the schedule's recurrence pattern
            const rrule = TherapistController.createRRule(
              schedule.recurrence,
              moment(firstFutureDate.fromDate)
            );

            const newRecurrenceData = {
              fromDate: firstFutureDate.fromDate.toISOString(),
              toDate: firstFutureDate.toDate.toISOString(),
              _id: firstFutureDate._id,
              rrule: rrule,
              data: sessionsToCreateEvents,
            };

            try {
              const googleCalenderEvent =
                await GoogleCalendarService.addEventToCalender(
                  therapistId,
                  addEventPayload,
                  newRecurrenceData,
                  schedule._id
                );

              // Update the database with the new event details
              if (
                googleCalenderEvent.calenderEventId &&
                googleCalenderEvent.calenderEventId.length > 0
              ) {
                // Create a map to track which recurrence dates have been updated
                const updatedRecurrenceDates = new Set<string>();

                for (
                  let i = 0;
                  i < googleCalenderEvent.calenderEventId.length;
                  i++
                ) {
                  const event = googleCalenderEvent.calenderEventId[i];
                  // Find the matching recurrence date by index or date
                  let recurrenceDate = sessionsToCreateEvents[i];

                  if (!recurrenceDate && i < sessionsToCreateEvents.length) {
                    recurrenceDate = sessionsToCreateEvents[0]; // Fallback to first date if no match
                  }

                  // Skip sessions that should be preserved (past sessions)
                  if (
                    recurrenceDate &&
                    sessionsToPreserve.has(recurrenceDate._id.toString())
                  ) {
                    continue;
                  }

                  if (
                    recurrenceDate &&
                    !updatedRecurrenceDates.has(recurrenceDate._id.toString())
                  ) {
                    await ScheduleDao.reschedule(
                      therapistId,
                      recurrenceDate._id,
                      recurrenceDate.fromDate,
                      recurrenceDate.toDate,
                      googleCalenderEvent.link,
                      event._id || ""
                    );

                    // Mark this recurrence date as updated
                    updatedRecurrenceDates.add(recurrenceDate._id.toString());
                  }
                }

                // Check if any future recurrence dates weren't updated
                // Create a map to track which dates we've already processed to avoid duplicates
                const processedDates = new Map<string, boolean>();

                for (const recDate of sessionsToCreateEvents) {
                  // Skip sessions that should be preserved (past sessions)
                  if (sessionsToPreserve.has(recDate._id.toString())) {
                    continue;
                  }

                  if (!updatedRecurrenceDates.has(recDate._id.toString())) {
                    // Create a unique key for this date to avoid duplicates
                    const dateKey = moment(recDate.fromDate).format(
                      "YYYY-MM-DD"
                    );

                    // Skip if we've already processed this date
                    if (processedDates.has(dateKey)) {
                      continue;
                    }

                    // Mark this date as processed
                    processedDates.set(dateKey, true);

                    // Create an individual event for this recurrence date
                    const newRecurrenceData = {
                      fromDate: recDate.fromDate.toISOString(),
                      toDate: recDate.toDate.toISOString(),
                      _id: recDate._id,
                      rrule: undefined, // Create individual event, not recurring
                    };

                    try {
                      // Double-check if an event already exists for this date or nearby dates
                      const checkDate = moment(recDate.fromDate);
                      const datesToCheck = [
                        {
                          fromDate: recDate.fromDate,
                          toDate: recDate.toDate,
                        },
                      ];

                      // Check for events with various offsets to catch all potential duplicates
                      const offsets = [-2, -1, 1, 2]; // Check 2 days before/after to be thorough

                      for (const offset of offsets) {
                        datesToCheck.push({
                          fromDate: moment(checkDate)
                            .add(offset, "days")
                            .toDate(),
                          toDate: moment(recDate.toDate)
                            .add(offset, "days")
                            .toDate(),
                        });
                      }

                      const existingEvents =
                        await GoogleCalendarService.findEventsByTherapistAndDates(
                          therapistId,
                          datesToCheck
                        );

                      if (existingEvents && existingEvents.length > 0) {
                        continue;
                      }

                      const individualEvent =
                        await GoogleCalendarService.addEventToCalender(
                          therapistId,
                          addEventPayload,
                          newRecurrenceData,
                          schedule._id
                        );

                      if (
                        individualEvent.calenderEventId &&
                        individualEvent.calenderEventId.length > 0
                      ) {
                        const event = individualEvent.calenderEventId[0];
                        await ScheduleDao.reschedule(
                          therapistId,
                          recDate._id,
                          recDate.fromDate,
                          recDate.toDate,
                          individualEvent.link,
                          event._id || ""
                        );
                      }
                    } catch (error) {
                      console.error(
                        `ERROR: Failed to create individual event for recurrence date ${recDate._id}:`,
                        error
                      );
                    }
                  }
                }
              }
            } catch (error) {
              console.error(
                `ERROR: Failed to create recurring event series:`,
                error
              );

              // Fall back to creating individual events if recurring creation fails

              // Create a map to track which dates we've already processed to avoid duplicates
              const processedDates = new Map<string, boolean>();

              // Create individual events for each future recurrence date
              for (const recDate of sessionsToCreateEvents) {
                // Skip sessions that should be preserved (past sessions)
                if (sessionsToPreserve.has(recDate._id.toString())) {
                  continue;
                }

                // Create a unique key for this date to avoid duplicates
                const dateKey = moment(recDate.fromDate).format("YYYY-MM-DD");

                // Skip if we've already processed this date
                if (processedDates.has(dateKey)) {
                  continue;
                }

                // Mark this date as processed
                processedDates.set(dateKey, true);

                const newRecurrenceData = {
                  fromDate: recDate.fromDate.toISOString(),
                  toDate: recDate.toDate.toISOString(),
                  _id: recDate._id,
                  rrule: undefined, // Create individual event, not recurring
                };

                try {
                  // Double-check if an event already exists for this date or nearby dates
                  const checkDate = moment(recDate.fromDate);
                  const datesToCheck = [
                    {
                      fromDate: checkDate.clone().toDate(),
                      toDate: moment(recDate.toDate).clone().toDate(),
                    },
                  ];

                  // Check for events with various offsets to catch all potential duplicates
                  const offsets = [-2, -1, 1, 2]; // Check 2 days before/after to be thorough

                  for (const offset of offsets) {
                    datesToCheck.push({
                      fromDate: moment(checkDate).add(offset, "days").toDate(),
                      toDate: moment(recDate.toDate)
                        .add(offset, "days")
                        .toDate(),
                    });
                  }

                  const existingEvents =
                    await GoogleCalendarService.findEventsByTherapistAndDates(
                      therapistId,
                      datesToCheck
                    );

                  if (existingEvents && existingEvents.length > 0) {
                    continue;
                  }

                  const googleCalenderEvent =
                    await GoogleCalendarService.addEventToCalender(
                      therapistId,
                      addEventPayload,
                      newRecurrenceData,
                      schedule._id
                    );

                  if (
                    googleCalenderEvent.calenderEventId &&
                    googleCalenderEvent.calenderEventId.length > 0
                  ) {
                    const event = googleCalenderEvent.calenderEventId[0];
                    await ScheduleDao.reschedule(
                      therapistId,
                      recDate._id,
                      recDate.fromDate,
                      recDate.toDate,
                      googleCalenderEvent.link,
                      event._id || ""
                    );
                  }
                } catch (error) {
                  console.error(
                    `ERROR: Failed to create individual event for recurrence date ${recDate._id}:`,
                    error
                  );
                }
              }
            }
          } else {
            // For non-recurring schedules or single future dates, create individual events for the selected session and future sessions

            // Create a map to track which dates we've already processed to avoid duplicates
            const processedDates = new Map<string, boolean>();

            // Create individual events for each future recurrence date
            for (const recDate of futureRecurrenceDates) {
              // Create a unique key for this date to avoid duplicates
              const dateKey = moment(recDate.fromDate).format("YYYY-MM-DD");

              // Skip if we've already processed this date
              if (processedDates.has(dateKey)) {
                continue;
              }

              // Mark this date as processed
              processedDates.set(dateKey, true);

              const newRecurrenceData = {
                fromDate: recDate.fromDate.toISOString(),
                toDate: recDate.toDate.toISOString(),
                _id: recDate._id,
                rrule: undefined, // Create individual event, not recurring
              };

              try {
                // Double-check if an event already exists for this date or nearby dates
                const checkDate = moment(recDate.fromDate);
                const datesToCheck = [
                  {
                    fromDate: checkDate.clone().toDate(),
                    toDate: moment(recDate.toDate).clone().toDate(),
                  },
                ];

                // Check for events with various offsets to catch all potential duplicates
                const offsets = [-2, -1, 1, 2]; // Check 2 days before/after to be thorough

                for (const offset of offsets) {
                  datesToCheck.push({
                    fromDate: moment(checkDate).add(offset, "days").toDate(),
                    toDate: moment(recDate.toDate).add(offset, "days").toDate(),
                  });
                }

                const existingEvents =
                  await GoogleCalendarService.findEventsByTherapistAndDates(
                    therapistId,
                    datesToCheck
                  );

                if (existingEvents && existingEvents.length > 0) {
                  continue;
                }

                const googleCalenderEvent =
                  await GoogleCalendarService.addEventToCalender(
                    therapistId,
                    addEventPayload,
                    newRecurrenceData,
                    schedule._id
                  );

                if (
                  googleCalenderEvent.calenderEventId &&
                  googleCalenderEvent.calenderEventId.length > 0
                ) {
                  const event = googleCalenderEvent.calenderEventId[0];
                  await ScheduleDao.reschedule(
                    therapistId,
                    recDate._id,
                    recDate.fromDate,
                    recDate.toDate,
                    googleCalenderEvent.link,
                    event._id || ""
                  );
                }
              } catch (error) {
                console.error(
                  `ERROR: Failed to create individual event for recurrence date ${recDate._id}:`,
                  error
                );
              }
            }
          }
        } else {
          // If offline session, just update the schedule for future dates

          for (const recurrenceDate of futureRecurrenceDates) {
            // Skip sessions that should be preserved (past sessions)
            if (sessionsToPreserve.has(recurrenceDate._id.toString())) {
              continue;
            }

            await ScheduleDao.reschedule(
              therapistId,
              recurrenceDate._id,
              recurrenceDate.fromDate,
              recurrenceDate.toDate,
              "",
              undefined
            );
          }
        }

        // Save the schedule
        await schedule.save();
      }

      return {
        success: true,
        message: "Recurring event series rescheduled successfully.",
        conflicts: [], // Return empty conflicts array if no conflicts were found
      };
    } catch (error: any) {
      console.error("ERROR in batchUpdateEvents:", error);
      return {
        success: false,
        message: error.message || "Failed to reschedule recurring events.",
      };
    }
  }

  // Helper function to create a recurrence rule (RRULE) for Google Calendar
  static createRRule(
    recurrencePattern: string,
    startDate: moment.Moment
  ): string {
    // Default to no recurrence
    if (!recurrencePattern || recurrencePattern === "Does Not Repeat") {
      return "";
    }

    // Parse the recurrence pattern
    const pattern = recurrencePattern.toLowerCase();

    // Get the day of week as a number (0 = Sunday, 1 = Monday, etc.)
    const dayOfWeek = startDate.day();

    // Format the day of week for RRULE (SU, MO, TU, etc.)
    const days = ["SU", "MO", "TU", "WE", "TH", "FR", "SA"];
    const rruleDay = days[dayOfWeek];

    // Create the appropriate RRULE
    if (pattern.includes("every day")) {
      return `RRULE:FREQ=DAILY;COUNT=30`; // Daily recurrence for 30 occurrences
    } else if (pattern.includes("every week")) {
      return `RRULE:FREQ=WEEKLY;BYDAY=${rruleDay};COUNT=12`; // Weekly recurrence for 12 weeks
    } else if (pattern.includes("every two weeks")) {
      return `RRULE:FREQ=WEEKLY;INTERVAL=2;BYDAY=${rruleDay};COUNT=6`; // Biweekly recurrence for 12 weeks
    } else if (pattern.includes("every month")) {
      // Monthly on the same day of month
      const dayOfMonth = startDate.date();
      return `RRULE:FREQ=MONTHLY;BYMONTHDAY=${dayOfMonth};COUNT=6`; // Monthly recurrence for 6 months
    }

    // Default to no recurrence if pattern not recognized
    return "";
  }

  // Helper function to create individual events
  static async createIndividualEvents(
    therapistId: string,
    addEventPayload: IAddEventPayload,
    recurrenceDates: any[],
    scheduleId: string
  ) {
    for (const recurrenceDate of recurrenceDates) {
      const newRecurrenceData = {
        fromDate: recurrenceDate.fromDate.toISOString(),
        toDate: recurrenceDate.toDate.toISOString(),
        _id: recurrenceDate._id,
        rrule: undefined, // Create individual events, not recurring
      };

      try {
        const googleCalenderEvent =
          await GoogleCalendarService.addEventToCalender(
            therapistId,
            addEventPayload,
            newRecurrenceData,
            scheduleId
          );

        // Update the database with the new event details
        if (
          googleCalenderEvent.calenderEventId &&
          googleCalenderEvent.calenderEventId.length > 0
        ) {
          for (const event of googleCalenderEvent.calenderEventId) {
            await ScheduleDao.reschedule(
              therapistId,
              recurrenceDate._id,
              recurrenceDate.fromDate,
              recurrenceDate.toDate,
              googleCalenderEvent.link,
              event._id || ""
            );
          }
        }
      } catch (error) {
        console.error(
          `ERROR: Failed to create new event for recurrence date ${recurrenceDate._id}:`,
          error
        );
        // Continue with the next recurrence date
      }
    }
  }

  static async updateMenu(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      let paymentGateway = req.body.paymentGateway
        ? req.body.paymentGateway
        : false;
      let paymentTracker = req.body.paymentTracker
        ? req.body.paymentTracker
        : true;

      req.therapist.menus.paymentGateway =
        req.therapist.menus.paymentGateway == false ? false : paymentGateway;
      req.therapist.menus.paymentTracker =
        req.therapist.menus.paymentTracker == true ? true : paymentTracker;

      await req.therapist.save();
      res.send("Menu updated successfully.");
    } catch (e) {
      next(e);
    }
  }

  static async exportTransactionCSV(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const therapist = await TherapistService.getTherapist(therapistId);
      if (!therapist) {
        return res
          .status(404)
          .send({ success: false, message: "No therapist found." });
      }

      const subscription: any =
        await TherapistSubscriptionService.getPaidByTherapistId(therapistId);
      const transactions: any =
        await TransactionService.getAllPopulatedTransactionsofTherapist(
          therapist
        );
      const payouts = await PayoutService.getTherapistPayouts(
        therapist,
        PayoutStatus.PAID
      );

      const therapistRecords = [
        {
          therapistId: therapist.identifier,
          therapistName: therapist.name,
          subscriptionStartDate:
            subscription.length > 0
              ? moment(subscription[subscription.length - 1].createdAt).format(
                  "YYYY-MM-DD"
                )
              : "--",
          firstPaymentLinkDate:
            transactions.length > 0
              ? moment(transactions[0].createdAt).format("YYYY-MM-DD")
              : "--",
          totalPaymentsCollected: transactions.filter(
            (transaction: any) =>
              transaction.paymentStatus == paymentStatus.COMPLETE
          ).length,
          totalAmountCredited: payouts.reduce(
            (total: number, current: any) => total + current.amountToBePaid,
            0
          ),
        },
      ];

      let clientRecords: any[] = [];
      for (const transaction of transactions) {
        const transaction_det: any = transaction;
        const client: any = transaction.clientId;
        const schedule: any = transaction.scheduleId;

        const recurrence = schedule?.recurrenceDates?.find(
          (rec: any) => String(rec._id) === String(transaction.scheduleRecId)
        );
        const finalAmountAfterGatewayCharges =
          transaction.amountReceived -
          (transaction.gatewayCharges.gatewayFee +
            transaction.gatewayCharges.gatewayTax);
        clientRecords.push({
          clientId: client?.clientId || "--",
          clientEmail: client?.email || "--",
          sessionDate: recurrence?.toDate
            ? moment(recurrence?.toDate).format("YYYY-MM-DD")
            : "--",
          sessionAmount: transaction.amount || "--",
          sessionCredited: finalAmountAfterGatewayCharges || "--",
          paymentLinkCreationDate: transaction_det.createdAt
            ? moment(transaction_det.createdAt).format("YYYY-MM-DD hh:mm a")
            : "--",
          paymentDate: transaction_det?.paymentDetails?.payload?.payment?.entity
            ?.created_at
            ? moment
                .unix(
                  transaction_det?.paymentDetails?.payload?.payment?.entity
                    ?.created_at
                )
                .format("YYYY-MM-DD hh:mm a")
            : "--",
          status: transaction.paymentStatus || "--",
          paymentMethod: transaction_det?.paymentDetails?.payload?.payment
            ?.entity?.method
            ? transaction_det?.paymentDetails?.payload?.payment?.entity?.method
            : "--",
          paymentLink: transaction.paymentLink || "--",
          cancellationFee: transaction.isFine ? "Yes" : "No",
        });
      }

      const workbook = new ExcelJS.Workbook();

      // Create a worksheet for therapist information
      const therapistSheet = workbook.addWorksheet("Therapist Info");
      therapistSheet.columns = [
        { header: "Therapist ID", key: "therapistId", width: 15 },
        { header: "Therapist Name", key: "therapistName", width: 25 },
        {
          header: "Subscription Start date",
          key: "subscriptionStartDate",
          width: 20,
        },
        {
          header: "Date first payment link was created",
          key: "firstPaymentLinkDate",
          width: 25,
        },
        {
          header: "Total payments collected (N)",
          key: "totalPaymentsCollected",
          width: 20,
        },
        {
          header: "Total amount credited so far",
          key: "totalAmountCredited",
          width: 20,
        },
      ];
      therapistSheet.addRows(therapistRecords);

      // Create a worksheet for client information
      const clientSheet = workbook.addWorksheet("Client Info");
      clientSheet.columns = [
        { header: "Client ID", key: "clientId", width: 15 },
        { header: "Client email", key: "clientEmail", width: 25 },
        { header: "Date of session", key: "sessionDate", width: 20 },
        { header: "Session Amount", key: "sessionAmount", width: 15 },
        {
          header: "Session credited (after Rzp charges)",
          key: "sessionCredited",
          width: 30,
        },
        {
          header: "Date of payment link creation",
          key: "paymentLinkCreationDate",
          width: 25,
        },
        {
          header: "Date payment was made by client",
          key: "paymentDate",
          width: 25,
        },
        { header: "Status (paid/pending/cancelled)", key: "status", width: 20 },
        {
          header: "Method of payment by client (credit card, UPI etc.)",
          key: "paymentMethod",
          width: 25,
        },
        { header: "Payment link", key: "paymentLink", width: 25 },
        { header: "Is Cancellation Fee", key: "cancellationFee", width: 25 },
      ];
      clientSheet.addRows(clientRecords);

      // Write the workbook to a file
      const savePath = path.join(
        CONFIG.cacheFolderPath,
        `Therapist_${therapistId}_Transaction_Records.xlsx`
      );
      await workbook.xlsx.writeFile(savePath);
      const link = await FileUploadService.uploadFile(
        fs.readFileSync(savePath),
        `Therapist_${therapistId}_Transaction_Records.xlsx`
      );
      res.send({ link: link?.Location });
    } catch (e) {
      next(e);
    }
  }

  static async uploadFile(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      if (!req.file)
        return res
          .status(404)
          .json({ success: false, message: "No File uploaded" });
      let file = req.file;

      const blob = fs.readFileSync(file.path);

      const fileUpload = await FileUploadService.uploadFile(
        blob,
        file.filename
      );
      if (!fileUpload) {
        return res.status(400).json({
          success: false,
          message: "There was some problem Uploading!",
        });
      }

      res.send(
        new Response(fileUpload.Location, "File Uploaded Successfully.")
      );
    } catch (error) {
      next(error);
    }
  }

  // static async RetriveFreeSlot(
  //   req: express.Request,
  //   res: express.Response,
  //   next: express.NextFunction
  // ) {
  //   try {
  //     const therapistId = req.therapist._id;
  //     const currentWeek = await Utility.getCurrentWeek();

  //     const bookSlot: any = await ScheduleModel.find(
  //       {
  //         therapistId: String(therapistId),
  //         "recurrenceDates.fromDate": {
  //           $gte: new Date(currentWeek.startDate),
  //           $lte: new Date(currentWeek.endDate),
  //         },
  //       },
  //       {
  //         _id: 1,
  //         "recurrenceDates.fromDate": 1,
  //         "recurrenceDates.toDate": 1,
  //       },
  //       {
  //         sort: {
  //           "recurrenceDates.fromDate": 1,
  //         },
  //       }
  //     );

  //     console.log(bookSlot);
  //     console.log(bookSlot?.recurrenceDates);

  //     const allSlots: { fromDate: Date; toDate: Date }[] =
  //       await Utility.allPossibleSlot(
  //         currentWeek.startDate,
  //         currentWeek.endDate
  //       );

  //     const freeSlots = allSlots.filter(
  //       (slot: { fromDate: Date; toDate: Date }) => {
  //         return !bookSlot.some((booked: any) =>
  //           Utility.isOverlapping(slot, booked.recurrenceDates)
  //         );
  //       }
  //     );

  //     const firstFourFreeSlots = freeSlots.slice(0, 4);

  //     return res.status(200).json({ slots: firstFourFreeSlots });
  //   } catch (error) {
  //     console.log(error);
  //     next(error);
  //   }
  // }

  // static async RetriveFreeSlot(
  //   req: express.Request,
  //   res: express.Response,
  //   next: express.NextFunction
  // ) {
  //   try {
  //     const therapistId = req.therapist._id;
  //     const currentWeek = await Utility.getCurrentWeek();
  //     const currentTime = new Date();

  //     // Query for booked slots within the current week
  //     const bookSlot: any[] = await ScheduleModel.find(
  //       {
  //         therapistId: String(therapistId),
  //         "recurrenceDates.fromDate": {
  //           $gte: new Date(currentWeek.startDate),
  //           $lte: new Date(currentWeek.endDate),
  //         },
  //       },
  //       {
  //         _id: 1,
  //         "recurrenceDates.fromDate": 1,
  //         "recurrenceDates.toDate": 1,
  //       },
  //       {
  //         sort: {
  //           "recurrenceDates.fromDate": 1,
  //         },
  //       }
  //     );

  //     // Extract all recurrenceDates from bookSlot
  //     const bookedRecurrenceDates = bookSlot.flatMap(
  //       (slot) => slot.recurrenceDates
  //     );

  //     // Generate all possible slots for the current week
  //     const allSlots: { fromDate: Date; toDate: Date }[] =
  //       await Utility.allPossibleSlot(
  //         currentWeek.startDate,
  //         currentWeek.endDate
  //       );

  //     // Filter out booked slots
  //     // const freeSlots = allSlots.filter((slot) => {
  //     //   const isAfterCurrentTime = slot.fromDate > currentTime;
  //     //   const isNotBooked = !bookedRecurrenceDates.some((booked: any) =>
  //     //     Utility.isOverlapping(slot, booked)
  //     //   );
  //     //   return isAfterCurrentTime && isNotBooked;
  //     // });

  //     const freeSlotsPerDay: {
  //       [key: string]: { fromDate: Date; toDate: Date };
  //     } = {};
  //     const freeSlots: { fromDate: Date; toDate: Date }[] = [];

  //     // Filter through all possible slots
  //     allSlots.forEach((slot) => {
  //       const isAfterCurrentTime = slot.fromDate > currentTime;
  //       const isNotBooked = !bookedRecurrenceDates.some((booked: any) =>
  //         Utility.isOverlapping(slot, booked)
  //       );

  //       // Exclude weekends (Saturday is 6, Sunday is 0)
  //       const dayOfWeek = slot.fromDate.getUTCDay();
  //       const isWeekday = dayOfWeek !== 0 && dayOfWeek !== 6;

  //       if (isAfterCurrentTime && isNotBooked && isWeekday) {
  //         // Create a unique key for each day based on year, month, and date
  //         const dayKey = `${slot.fromDate.getUTCFullYear()}-${slot.fromDate.getUTCMonth()}-${slot.fromDate.getUTCDate()}`;

  //         // Check if a slot for this day has already been added
  //         if (!freeSlotsPerDay[dayKey]) {
  //           freeSlotsPerDay[dayKey] = slot;
  //           freeSlots.push(slot);
  //         }
  //       }
  //     });

  //     // Return the free slots in the response
  //     return res.status(200).json({ slots: freeSlots });
  //   } catch (error) {
  //     console.log(error);
  //     next(error);
  //   }
  // }

  static async RetriveFreeSlot(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      // const currentWeek = await Utility.getCurrentWeek();
      const currentTime = new Date();

      // Query for booked slots within the current week
      const bookSlot: any[] = await ScheduleModel.aggregate([
        {
          $unwind: "$recurrenceDates",
        },
        {
          $match: {
            therapistId: new mongoose.Types.ObjectId(therapistId),
            "recurrenceDates.fromDate": {
              $gte: currentTime,
              // $lte: new Date(currentWeek.endDate),
            },
            "recurrenceDates.status": {
              $nin: [ScheduleStatus.CANCELLED],
            },
          },
        },
        {
          $project: {
            _id: 1,
            "recurrenceDates.fromDate": 1,
            "recurrenceDates.toDate": 1,
          },
        },
        {
          $sort: {
            "recurrenceDates.fromDate": 1,
          },
        },
      ]);

      const workingHoursStart = 3; // Working starts at 3:30 AM
      const workingMinutesStart = 30; // Start minutes at 30
      const workingHoursEnd = 15; // Working ends at 3:30 PM
      const workingMinutesEnd = 30; // End minutes at 30
      const slotDuration = 60 * 60 * 1000; // 1 hour in milliseconds
      const freeSlots: { fromDate: Date; toDate: Date }[] = [];

      let dayOffset = 0;

      // Continue until we find at least 5 free slots
      while (freeSlots.length < 5) {
        const slots: { fromDate: Date; toDate: Date }[] = [];

        // Generate slots for the next 1 days starting from dayOffset
        for (let i = 0; i < 1; i++) {
          const dayStart = new Date(Date.now());
          dayStart.setUTCDate(currentTime.getUTCDate() + dayOffset + i);
          dayStart.setUTCHours(workingHoursStart, workingMinutesStart, 0, 0);

          const dayEnd = new Date(dayStart);
          dayEnd.setUTCHours(workingHoursEnd, workingMinutesEnd, 0, 0);

          for (
            let time = dayStart.getTime();
            time < dayEnd.getTime();
            time += slotDuration
          ) {
            const slot = {
              fromDate: new Date(time),
              toDate: new Date(time + slotDuration),
            };

            // Only include slots after the current time
            if (slot.fromDate > currentTime) {
              slots.push(slot);
            }
          }
        }

        // Filter out booked slots
        const availableSlots = slots.filter((slot) => {
          return !bookSlot.some((booked) => {
            const bookedFrom = new Date(
              booked.recurrenceDates.fromDate
            ).getTime();
            const bookedTo = new Date(booked.recurrenceDates.toDate).getTime();
            const slotFrom = slot.fromDate.getTime();
            const slotTo = slot.toDate.getTime();

            // Check if the slot overlaps with booked time
            return (
              (slotFrom >= bookedFrom && slotFrom < bookedTo) ||
              (slotTo > bookedFrom && slotTo <= bookedTo) ||
              (slotFrom <= bookedFrom && slotTo >= bookedTo)
            );
          });
        });

        // let eixstedEvent: any[] = [];

        // Query events for the entire week in one request
        const events = await GoogleCalendarService.eventByDate(
          req.therapist._id,
          200,
          availableSlots[0]?.fromDate,
          availableSlots[availableSlots.length - 1]?.toDate
        );

        // Process the events to extract the time ranges
        // if (events && events.length > 0) {
        //   eixstedEvent = events
        //     .filter(
        //       (event: any) => event?.start?.dateTime && event?.end?.dateTime
        //     )
        //     .map((event: any) => ({
        //       fromDate: new Date(event.start.dateTime),
        //       toDate: new Date(event.end.dateTime),
        //     }));
        // }

        // Filter availableSlots to exclude overlapping slots with eixstedEvent
        const filteredAvailableSlots = availableSlots.filter((slot) => {
          return !events.some((event: any) => {
            const eventFrom = new Date(event.start.dateTime).getTime();
            const eventTo = new Date(event.end.dateTime).getTime();
            const slotFrom = slot.fromDate.getTime();
            const slotTo = slot.toDate.getTime();

            // Check if the slot overlaps with the event
            return (
              (slotFrom >= eventFrom && slotFrom < eventTo) || // Slot starts during the event
              (slotTo > eventFrom && slotTo <= eventTo) || // Slot ends during the event
              (slotFrom <= eventFrom && slotTo >= eventTo) // Slot fully covers the event
            );
          });
        });

        // Add newly found slots to freeSlots
        freeSlots.push(...filteredAvailableSlots);

        // If we have found at least 5 free slots, break the loop
        if (freeSlots.length >= 5) {
          return res.status(200).json({ slots: freeSlots.slice(0, 5) });
        }

        // Increment the dayOffset to search the next week
        dayOffset += 1;

        // Break if no more slots are found to avoid infinite loop
        if (availableSlots.length === 0 && freeSlots.length < 5) {
          break;
        }
      }

      // If less than 5 slots are found, return what we have
      return res.status(200).json({ slots: freeSlots });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  /**
   * Check for conflicts when rescheduling a therapy session
   *
   * @param req Express request object
   * @param res Express response object
   * @param next Express next function
   */
  static async checkRescheduleConflict(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const { type, newDate, newStartDate, recurrencePattern, duration } =
        req.body;

      // Validate required parameters
      if (!type || (type !== "single" && type !== "entire")) {
        return res.status(200).json({
          success: false,
          message: "Invalid type parameter. Must be 'single' or 'entire'.",
          conflicts: [],
          totalConflicts: 0,
          hasConflicts: false,
          conflictDates: [],
        });
      }

      if (type === "single" && !newDate) {
        return res.status(200).json({
          success: false,
          message: "newDate is required for single session rescheduling.",
          conflicts: [],
          totalConflicts: 0,
          hasConflicts: false,
          conflictDates: [],
        });
      }

      if (type === "entire" && (!newStartDate || !recurrencePattern)) {
        return res.status(200).json({
          success: false,
          message:
            "newStartDate and recurrencePattern are required for entire series rescheduling.",
          conflicts: [],
          totalConflicts: 0,
          hasConflicts: false,
          conflictDates: [],
        });
      }

      // Check for conflicts based on type
      let result;
      if (type === "single") {
        result = await RescheduleConflictService.checkSingleSessionConflict(
          therapistId,
          newDate,
          duration || 60 // Default to 60 minutes if not provided
        );
      } else {
        result = await RescheduleConflictService.checkEntireSeriesConflict(
          therapistId,
          newStartDate,
          recurrencePattern,
          duration || 60 // Default to 60 minutes if not provided
        );
      }

      // Always return 200 status code with appropriate message and conflict data
      return res.status(200).json(result);
    } catch (error) {
      console.error("Error checking for reschedule conflicts:", error);
      return res.status(200).json({
        success: false,
        message: "Error checking for conflicts: " + (error as Error).message,
        conflicts: [],
        totalConflicts: 0,
        hasConflicts: false,
        conflictDates: [],
      });
    }
  }

  static async therapistOnboarding(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const payload = JSON.parse(req.body.onboardingData);

      if (!req.file)
        return res
          .status(400)
          .send(getErrorResponse("VALIDATION_ERROR", "No File uploaded"));

      const file = req.file;

      const ext = path.extname(file.originalname).toLowerCase();

      const allowedExtensions = [".jpg", ".jpeg", ".png"];
      if (!allowedExtensions.includes(ext)) {
        // deleting file from local
        fs.unlinkSync(file.path);
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Invalid file type. Only JPG, JPEG, and PNG are allowed."
            )
          );
      }

      const blob = fs.readFileSync(file.path);

      const fileUpload = await FileUploadService.onboardingUploadFile(
        blob,
        file.filename
      );
      if (!fileUpload) {
        // deleting file from local
        fs.unlinkSync(file.path);
        return res
          .status(500)
          .send(
            getErrorResponse(
              "INTERNAL_SERVER_ERROR",
              "There was some problem Uploading the file!"
            )
          );
      }

      // getting the public URL of the file uploaded
      const publicUrl = fileUpload.Location;

      const updateData: any = {
        name: payload.name,
        pronouns: payload.pronouns,
        gender: payload.gender,
        therapyTypes: payload.therapyTypes,
        languages: payload.languages,
        minFee: payload.minFee,
        maxFee: payload.maxFee,
        location: payload.location,
        slotType: payload.slotType,
        timeZone: payload.timeZone,
        professionalQualification: payload.professionalQualification,
        values: payload.values,
        concerns: payload.concerns,
        practiceApproach: payload.practiceApproach,
        "verificationDetails.yearsOfExperience": payload.yearsOfExperience,
        "verificationDetails.practicingTitle": payload.designation,
        profilePicUrl: publicUrl,
        fromPublicCalender: true,
      };

      // getting therapist data to setup thoughtpudding URL
      const therapistData = await TherapistDao.getTherapist(therapistId);

      if (!therapistData) {
        // deleting file from local
        fs.unlinkSync(file.path);
        return res
          .status(500)
          .send(
            getErrorResponse("INTERNAL_SERVER_ERROR", "Therapist not found")
          );
      }

      const identifier = therapistData.identifier;
      const environment = process.env.ENVIRONMENT;

      let thoughtPuddingBaseUrl;
      if (environment === "DEV") {
        thoughtPuddingBaseUrl = process.env.THOUGHTPUDDING_DEV_BASEURL;
      } else if (environment === "UAT") {
        thoughtPuddingBaseUrl = process.env.THOUGHTPUDDING_UAT_BASEURL;
      } else if (environment === "PROD") {
        thoughtPuddingBaseUrl = process.env.THOUGHTPUDDING_BASEURL;
      }
      const bookingURL = `${thoughtPuddingBaseUrl}/clients/${identifier}`;

      updateData["bookingURL"] = bookingURL;

      const onboardedData = await TherapistDao.updateTherapistData(
        therapistId,
        updateData
      );

      if (!onboardedData) {
        // deleting file from local
        fs.unlinkSync(file.path);
        return res
          .status(500)
          .send(
            getErrorResponse(
              "INTERNAL_SERVER_ERROR",
              "Therapist onboarding error"
            )
          );
      }

      // deleting file from local
      fs.unlinkSync(file.path);

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Therapist Onboarded successfully",
        data: onboardedData,
      });
    } catch (error) {
      console.log(error);
      if (req.file && fs.existsSync(req.file.path)) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (unlinkErr) {
          console.error(
            "Failed to delete uploaded file in catch block:",
            unlinkErr
          );
        }
      }
      next(error);
    }
  }

  static async therapistProfileUpdate(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      if (!req?.body?.onboardingData && !req.file) {
        return res
          .status(400)
          .send(getErrorResponse("VALIDATION_ERROR", "Invalid Request"));
      }

      let payload;
      if (req?.body?.onboardingData) {
        payload = JSON.parse(req.body.onboardingData);
      }

      let publicUrl;

      if (req.file) {
        const file = req.file;

        const ext = path.extname(file.originalname).toLowerCase();

        const allowedExtensions = [".jpg", ".jpeg", ".png"];
        if (!allowedExtensions.includes(ext)) {
          // deleting file from local
          fs.unlinkSync(file.path);
          return res
            .status(400)
            .send(
              getErrorResponse(
                "VALIDATION_ERROR",
                "Invalid file type. Only JPG, JPEG, and PNG are allowed."
              )
            );
        }

        const blob = fs.readFileSync(file.path);

        const fileUpload = await FileUploadService.onboardingUploadFile(
          blob,
          file.filename
        );
        if (!fileUpload) {
          // deleting file from local
          fs.unlinkSync(file.path);
          return res
            .status(500)
            .send(
              getErrorResponse(
                "INTERNAL_SERVER_ERROR",
                "There was some problem Uploading the file!"
              )
            );
        }

        // getting the public URL of the file uploaded
        publicUrl = fileUpload.Location;

        // deleting file from local
        fs.unlinkSync(file.path);
      }

      const payloadFields = [
        "name",
        "pronouns",
        "gender",
        "therapyTypes",
        "languages",
        "minFee",
        "maxFee",
        "location",
        "slotType",
        "timeZone",
        "professionalQualification",
        "values",
        "concerns",
        "practiceApproach",
        "bookingMessage",
      ];

      const updateData: any = {};

      // preparing update data
      if (payload) {
        for (const field of payloadFields) {
          if (payload[field]) {
            updateData[field] = payload[field];
          }
        }

        if (payload.yearsOfExperience) {
          updateData["verificationDetails.yearsOfExperience"] =
            payload.yearsOfExperience;
        }
        if (payload.designation) {
          updateData["verificationDetails.practicingTitle"] =
            payload.designation;
        }
      }

      // Add profilePicUrl if available
      if (publicUrl) {
        updateData["profilePicUrl"] = publicUrl;
      }

      const onboardedData = await TherapistDao.updateTherapistData(
        therapistId,
        updateData
      );

      if (!onboardedData) {
        return res
          .status(500)
          .send(
            getErrorResponse(
              "INTERNAL_SERVER_ERROR",
              "Therapist profile update error"
            )
          );
      }

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Therapist profile updated successfully",
        data: onboardedData,
      });
    } catch (error) {
      console.log(error);
      if (req.file && fs.existsSync(req.file.path)) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (unlinkErr) {
          console.error(
            "Failed to delete uploaded file in catch block:",
            unlinkErr
          );
        }
      }
      next(error);
    }
  }

  static async getTherapistProfile(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      const onboardedTherapistData: any =
        await TherapistDao.getOnboardedTherapistData(therapistId);

      if (!onboardedTherapistData && !onboardedTherapistData.length) {
        return res
          .status(404)
          .send(getErrorResponse("NO_DATA_FOUND", "No Therapist Data Found"));
      }

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Fetched Therapist profile data successfully",
        data: onboardedTherapistData,
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  static async refreshCalendarTokens(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const { bulkRefresh = false } = req.body;

      if (bulkRefresh) {
        // Admin function to refresh all therapist tokens
        const { OptimizedCalendarCron } = require('../crons/optimizedCalendar.cron');
        const result = await OptimizedCalendarCron.bulkRefreshAllTokens();

        return res.status(200).json({
          success: true,
          message: result.message,
          data: {
            successful: result.successful,
            failed: result.failed,
            totalProcessed: result.successful.length + result.failed.length
          }
        });
      } else {
        // Refresh tokens for current therapist only
        const { GoogleTokenManagerService } = require('../services/googleTokenManager.service');
        const googleCalendarData = await GoogleCalendarService.findByTherapist(therapistId);

        if (!googleCalendarData) {
          return res.status(404).json({
            success: false,
            message: "No Google Calendar connection found for this therapist"
          });
        }

        const refreshResult = await GoogleTokenManagerService.refreshAccessToken(googleCalendarData);

        if (refreshResult.success) {
          return res.status(200).json({
            success: true,
            message: "Calendar tokens refreshed successfully",
            data: {
              therapistId: therapistId,
              refreshedAt: new Date()
            }
          });
        } else {
          return res.status(400).json({
            success: false,
            message: `Token refresh failed: ${refreshResult.error}`,
            error: refreshResult.error
          });
        }
      }
    } catch (error: any) {
      console.error('Error refreshing calendar tokens:', error.message);
      return res.status(500).json({
        success: false,
        message: "Internal server error while refreshing tokens",
        error: error.message
      });
    }
  }
}
