/**
 * Test script for token refresh functionality
 * 
 * This script tests the new token refresh implementation
 * to ensure it works correctly before deploying to production.
 */

import { GoogleTokenManagerService } from '../services/googleTokenManager.service';
import { GoogleCalendarService } from '../services/googleCalendar.service';
import { TherapistDao } from '../lib/dao/therapist.dao';
import { OptimizedCalendarSyncService } from '../services/optimizedCalendarSync.service';
import { OptimizedCalendarCron } from '../crons/optimizedCalendar.cron';

export class TokenRefreshTestService {
  
  /**
   * Test token refresh for a single therapist
   */
  static async testSingleTherapistTokenRefresh(therapistId: string): Promise<void> {
    try {
      console.log(`🧪 Testing token refresh for therapist: ${therapistId}`);
      
      // Get Google Calendar data
      const googleCalendarData = await GoogleCalendarService.findByTherapist(therapistId);
      if (!googleCalendarData) {
        console.log(`❌ No Google Calendar data found for therapist: ${therapistId}`);
        return;
      }
      
      console.log(`📋 Current tokens for therapist ${therapistId}:`);
      console.log(`   Access token: ${googleCalendarData.access_token ? 'Present' : 'Missing'}`);
      console.log(`   Refresh token: ${googleCalendarData.refresh_token ? 'Present' : 'Missing'}`);
      
      // Test token validation
      const isValid = GoogleTokenManagerService.validateTokens(googleCalendarData);
      console.log(`✅ Token validation: ${isValid ? 'PASSED' : 'FAILED'}`);
      
      if (!isValid) {
        console.log(`❌ Skipping refresh test due to invalid tokens`);
        return;
      }
      
      // Test manual token refresh
      console.log(`🔄 Testing manual token refresh...`);
      const refreshResult = await GoogleTokenManagerService.refreshAccessToken(googleCalendarData);
      
      if (refreshResult.success) {
        console.log(`✅ Manual token refresh: SUCCESS`);
        console.log(`   New access token: ${refreshResult.googleCalendarData?.access_token ? 'Generated' : 'Missing'}`);
      } else {
        console.log(`❌ Manual token refresh: FAILED`);
        console.log(`   Error: ${refreshResult.error}`);
      }
      
      // Test sync with new tokens
      console.log(`🔄 Testing sync with refreshed tokens...`);
      const syncResult = await OptimizedCalendarSyncService.performIncrementalSync(therapistId, {
        incremental: true,
        maxResults: 10
      });
      
      if (syncResult.success) {
        console.log(`✅ Sync with refreshed tokens: SUCCESS`);
        console.log(`   Events processed: ${syncResult.syncedEvents}`);
        console.log(`   Errors: ${syncResult.errors.length}`);
      } else {
        console.log(`❌ Sync with refreshed tokens: FAILED`);
        console.log(`   Errors: ${JSON.stringify(syncResult.errors, null, 2)}`);
      }
      
    } catch (error: any) {
      console.error(`💥 Test failed for therapist ${therapistId}:`, error.message);
    }
  }
  
  /**
   * Test bulk token refresh
   */
  static async testBulkTokenRefresh(): Promise<void> {
    try {
      console.log(`🧪 Testing bulk token refresh...`);
      
      // Get all therapists with Google Calendar
      const allTherapists = await TherapistDao.getAllTherapists();
      const therapistIds = [];
      
      for (const therapist of allTherapists.slice(0, 3)) { // Test with first 3 therapists only
        const googleCalendar = await GoogleCalendarService.findByTherapist(therapist._id);
        if (googleCalendar && googleCalendar.access_token && googleCalendar.refresh_token) {
          therapistIds.push(therapist._id.toString());
        }
      }
      
      if (therapistIds.length === 0) {
        console.log(`❌ No therapists with valid Google Calendar tokens found`);
        return;
      }
      
      console.log(`📋 Testing bulk refresh for ${therapistIds.length} therapists`);
      
      const result = await GoogleTokenManagerService.bulkRefreshTokens(therapistIds);
      
      console.log(`✅ Bulk refresh completed:`);
      console.log(`   Successful: ${result.successful.length}`);
      console.log(`   Failed: ${result.failed.length}`);
      
      if (result.failed.length > 0) {
        console.log(`❌ Failed therapists:`);
        result.failed.forEach(failure => {
          console.log(`   - ${failure.therapistId}: ${failure.error}`);
        });
      }
      
    } catch (error: any) {
      console.error(`💥 Bulk test failed:`, error.message);
    }
  }
  
  /**
   * Test the cron service bulk refresh
   */
  static async testCronBulkRefresh(): Promise<void> {
    try {
      console.log(`🧪 Testing cron bulk refresh...`);
      
      const result = await OptimizedCalendarCron.bulkRefreshAllTokens();
      
      console.log(`✅ Cron bulk refresh completed:`);
      console.log(`   Message: ${result.message}`);
      console.log(`   Successful: ${result.successful?.length || 0}`);
      console.log(`   Failed: ${result.failed?.length || 0}`);
      
    } catch (error: any) {
      console.error(`💥 Cron test failed:`, error.message);
    }
  }
  
  /**
   * Run all tests
   */
  static async runAllTests(): Promise<void> {
    console.log(`🚀 Starting token refresh tests...\n`);
    
    try {
      // Get a test therapist
      const allTherapists = await TherapistDao.getAllTherapists();
      const testTherapist = allTherapists.find(async (therapist) => {
        const googleCalendar = await GoogleCalendarService.findByTherapist(therapist._id);
        return googleCalendar && googleCalendar.access_token && googleCalendar.refresh_token;
      });
      
      if (testTherapist) {
        await this.testSingleTherapistTokenRefresh(testTherapist._id.toString());
        console.log(`\n${'='.repeat(50)}\n`);
      }
      
      await this.testBulkTokenRefresh();
      console.log(`\n${'='.repeat(50)}\n`);
      
      await this.testCronBulkRefresh();
      
      console.log(`\n✅ All tests completed!`);
      
    } catch (error: any) {
      console.error(`💥 Test suite failed:`, error.message);
    }
  }
}

// Export for use in other scripts
export default TokenRefreshTestService;

// If running directly
if (require.main === module) {
  // Connect to database first
  const { DB } = require('../config/DB');
  
  DB.connect({
    isDbConnected: false,
    app: null
  }).then(() => {
    console.log('Database connected, starting tests...');
    TokenRefreshTestService.runAllTests().then(() => {
      process.exit(0);
    }).catch((error) => {
      console.error('Test failed:', error);
      process.exit(1);
    });
  }).catch((error) => {
    console.error('Database connection failed:', error);
    process.exit(1);
  });
}
