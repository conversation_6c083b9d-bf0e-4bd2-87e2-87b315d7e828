/**
 * Optimized Calendar Cron Service
 * 
 * This service provides intelligent calendar synchronization:
 * - Replaces the basic 10-second cron with smart scheduling
 * - Manages webhook renewals
 * - Performs incremental syncs
 * - Handles error recovery
 * - Monitors sync performance
 */

import cron from 'node-cron';
import { CalendarWebhookService } from '../services/calendarWebhook.service';
import { OptimizedCalendarSyncService } from '../services/optimizedCalendarSync.service';
import { EnhancedRescheduleDetectorService } from '../services/enhancedRescheduleDetector.service';
import { TherapistDao } from '../lib/dao/therapist.dao';
import { GoogleCalendarService } from '../services/googleCalendar.service';
import CalendarWebhookModel from '../models/CalendarWebhook.model';

export class OptimizedCalendarCron {
  private static isRunning = false;
  private static syncStats = {
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    lastSyncTime: null as Date | null
  };

  /**
   * Initialize all cron jobs
   */
  static async initializeCronJobs(): Promise<void> {
    console.log('Initializing optimized calendar cron jobs...');

    // Webhook renewal cron - runs every hour
    this.scheduleWebhookRenewal();

    // Incremental sync cron - runs every 5 minutes (much less frequent than 10 seconds)
    this.scheduleIncrementalSync();

    // Full sync cron - runs every 6 hours as backup
    this.scheduleFullSync();

    // Cleanup cron - runs daily
    this.scheduleCleanup();

    // Performance monitoring - runs every 30 minutes
    this.schedulePerformanceMonitoring();

    console.log('Optimized calendar cron jobs initialized successfully');
  }

  /**
   * Schedule webhook renewal checks
   */
  private static scheduleWebhookRenewal(): void {
    // Run every hour to check for expiring webhooks
    cron.schedule('0 * * * *', async () => {
      try {
        console.log('Checking for expiring webhooks...');
        await CalendarWebhookService.checkAndRenewExpiringWebhooks();
        
        // Also clean up expired webhooks in database
        await CalendarWebhookModel.deactivateExpiredWebhooks();
        
        console.log('Webhook renewal check completed');
      } catch (error: any) {
        console.error('Error in webhook renewal cron:', error.message);
      }
    });
  }

  /**
   * Schedule incremental sync for all therapists (backup for unreliable webhooks)
   */
  private static scheduleIncrementalSync(): void {
    // Run every 3 minutes for incremental sync (backup for webhooks)
    cron.schedule('*/3 * * * *', async () => {
      if (this.isRunning) {
        console.log('Incremental sync already running, skipping...');
        return;
      }

      this.isRunning = true;
      try {
        console.log('Starting incremental sync...');
        await this.performIncrementalSyncForAllTherapists();
        console.log('Incremental sync completed');
      } catch (error: any) {
        console.error('Error in incremental sync cron:', error.message);
      } finally {
        this.isRunning = false;
      }
    });
  }

  /**
   * Schedule full sync as backup
   */
  private static scheduleFullSync(): void {
    // Run every 6 hours for full sync
    cron.schedule('0 */6 * * *', async () => {
      try {
        console.log('Starting full sync backup...');
        await this.performFullSyncForAllTherapists();
        console.log('Full sync backup completed');
      } catch (error: any) {
        console.error('Error in full sync cron:', error.message);
      }
    });
  }

  /**
   * Schedule cleanup tasks
   */
  private static scheduleCleanup(): void {
    // Run daily at 2 AM
    cron.schedule('0 2 * * *', async () => {
      try {
        console.log('Starting daily cleanup...');
        await this.performDailyCleanup();
        console.log('Daily cleanup completed');
      } catch (error: any) {
        console.error('Error in cleanup cron:', error.message);
      }
    });
  }

  /**
   * Schedule performance monitoring
   */
  private static schedulePerformanceMonitoring(): void {
    // Run every 30 minutes
    cron.schedule('*/30 * * * *', async () => {
      try {
        await this.monitorPerformance();
      } catch (error: any) {
        console.error('Error in performance monitoring:', error.message);
      }
    });
  }

  /**
   * Perform incremental sync for all therapists
   * NOTE: Runs as backup even when webhooks are active since webhooks are unreliable
   */
  private static async performIncrementalSyncForAllTherapists(): Promise<void> {
    console.log(`🔄 Running automatic incremental sync for all therapists`);

    try {
      // Get all therapists with Google Calendar connections
      const allTherapists = await TherapistDao.getAllTherapists();
      let syncedTherapists = 0;
      let totalUpdated = 0;
      let totalCreated = 0;
      let totalErrors = 0;

      for (const therapist of allTherapists) {
        try {
          // Check if therapist has Google Calendar connected
          const googleCalendar = await GoogleCalendarService.findByTherapist(therapist._id);
          if (!googleCalendar || !googleCalendar.access_token) {
            continue; // Skip therapists without Google Calendar
          }

          // Check if webhook is active and recently received notifications
          const activeWebhook = await CalendarWebhookModel.findOne({
            therapistId: therapist._id,
            isActive: true,
            expiration: { $gt: new Date() }
          });

          // If webhook is active and received notifications in last 10 minutes, skip sync
          const recentNotification = activeWebhook?.lastNotificationAt &&
            (Date.now() - activeWebhook.lastNotificationAt.getTime()) < 600000; // 10 minutes

          if (recentNotification) {
            console.log(`⏭️ Skipping sync for therapist ${therapist._id} - webhook recently active`);
            continue;
          }

          // Perform sync for this therapist
          const syncResult = await OptimizedCalendarSyncService.performIncrementalSync(
            therapist._id.toString(),
            {
              incremental: true,
              maxResults: 100
            }
          );

          if (syncResult.success) {
            syncedTherapists++;
            totalUpdated += syncResult.updatedEvents || 0;
            totalCreated += syncResult.createdEvents || 0;

            // Log significant changes
            if (syncResult.updatedEvents > 0 || syncResult.createdEvents > 0) {
              console.log(`✅ Therapist ${therapist._id}: ${syncResult.updatedEvents} updated, ${syncResult.createdEvents} created`);
            }

            // Update sync statistics
            this.syncStats.successfulSyncs++;
          } else {
            totalErrors++;
            this.syncStats.failedSyncs++;
            console.error(`❌ Sync failed for therapist ${therapist._id}:`, syncResult.errors);
          }

        } catch (error: any) {
          totalErrors++;
          this.syncStats.failedSyncs++;
          console.error(`💥 Error syncing therapist ${therapist._id}:`, error.message);
        }
      }

      // Update global stats
      this.syncStats.totalSyncs++;
      this.syncStats.lastSyncTime = new Date();

      // Log summary if there were changes
      if (totalUpdated > 0 || totalCreated > 0 || totalErrors > 0) {
        console.log(`📊 Automatic sync summary: ${syncedTherapists} therapists synced, ${totalUpdated} updated, ${totalCreated} created, ${totalErrors} errors`);
      }

    } catch (error: any) {
      console.error('💥 Error in automatic incremental sync:', error.message);
      this.syncStats.failedSyncs++;
    }
  }

  /**
   * Perform full sync for all therapists (backup)
   */
  private static async performFullSyncForAllTherapists(): Promise<void> {
    const allTherapists = await TherapistDao.getAllTherapists();
    
    for (const therapist of allTherapists) {
      try {
        const googleCalendar = await GoogleCalendarService.findByTherapist(therapist._id);
        if (!googleCalendar) {
          continue;
        }

        const syncResult = await OptimizedCalendarSyncService.performIncrementalSync(
          therapist._id.toString(),
          {
            incremental: false, // Full sync
            maxResults: 200
          }
        );

        console.log(`Full sync for therapist ${therapist._id}: ${syncResult.syncedEvents} events processed`);

      } catch (error: any) {
        console.error(`Error in full sync for therapist ${therapist._id}:`, error.message);
      }
    }
  }

  /**
   * Perform daily cleanup tasks
   */
  private static async performDailyCleanup(): Promise<void> {
    try {
      // Clean up expired webhooks
      await CalendarWebhookModel.deactivateExpiredWebhooks();
      
      // Clean up old sync logs (if implemented)
      // await this.cleanupOldSyncLogs();
      
      // Reset daily statistics
      // await this.resetDailyStats();
      
      console.log('Daily cleanup completed successfully');
    } catch (error: any) {
      console.error('Error in daily cleanup:', error.message);
    }
  }

  /**
   * Monitor performance and log statistics
   */
  private static async monitorPerformance(): Promise<void> {
    try {
      const activeWebhooks = await CalendarWebhookModel.countDocuments({ isActive: true });
      const totalWebhooks = await CalendarWebhookModel.countDocuments();
      
      const performanceStats = {
        activeWebhooks,
        totalWebhooks,
        syncStats: this.syncStats,
        timestamp: new Date()
      };

      console.log('Performance Stats:', JSON.stringify(performanceStats, null, 2));

      // Here you could send stats to monitoring service
      // await this.sendStatsToMonitoring(performanceStats);

    } catch (error: any) {
      console.error('Error in performance monitoring:', error.message);
    }
  }

  /**
   * Get current sync statistics
   */
  static getSyncStatistics() {
    return {
      ...this.syncStats,
      isRunning: this.isRunning
    };
  }

  /**
   * Force sync for a specific therapist
   */
  static async forceSyncTherapist(therapistId: string): Promise<any> {
    try {
      const syncResult = await OptimizedCalendarSyncService.performIncrementalSync(
        therapistId,
        {
          incremental: false,
          maxResults: 250
        }
      );

      return {
        success: true,
        result: syncResult
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Stop all cron jobs (for graceful shutdown)
   */
  static stopAllCronJobs(): void {
    console.log('Stopping all calendar cron jobs...');
    // Note: node-cron doesn't provide a direct way to stop specific jobs
    // In production, you'd want to keep references to the cron jobs
    console.log('Calendar cron jobs stopped');
  }
}
